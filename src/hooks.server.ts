import type { Handle } from '@sveltejs/kit';

export const handle = (async ({ event, resolve }) => {
	return await resolve(event, {
		transformPageChunk: async ({ html }) => {
			return html
				.replace(
					'data-auth-client-id-replacement',
					(await import('$env/static/public')).PUBLIC_AUTH_CLIENT_ID ?? '$PUBLIC_AUTH_CLIENT_ID'
				)
				.replace(
					'data-auth-realm-replacement',
					(await import('$env/static/public')).PUBLIC_AUTH_REALM ?? '$PUBLIC_AUTH_REALM'
				)
				.replace(
					'data-auth-url-replacement',
					(await import('$env/static/public')).PUBLIC_AUTH_URL ?? '$PUBLIC_AUTH_URL'
				)
				.replace(
					'data-backend-url-replacement',
					(await import('$env/static/public')).PUBLIC_BACKEND_URL ?? '$PUBLIC_BACKEND_URL'
				)
				.replace(
					'data-ms-glider-url-replacement',
					(await import('$env/static/public')).PUBLIC_MS_GLIDER_URL ?? '$PUBLIC_MS_GLIDER_URL'
				);
		}
	});
}) satisfies Handle;
