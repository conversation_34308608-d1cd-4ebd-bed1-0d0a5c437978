<script lang="ts">
	import * as Card from '$lib/components/ui/card';
	import { ScrollArea } from '$lib/components/ui/scroll-area/index.js';
	import { Badge } from '$lib/components/ui/badge';
	import { onMount, onDestroy } from 'svelte';
	import {
		fetchTestProcedures,
		fetchTests,
		fetchPx4Versions,
		fetchGliderCompanionVersions
	} from '$lib/api';
	import type { TestProcedure, Test, SoftwareVersion } from '$lib/types';
	import { parseVersion } from '$lib/utils';
	import OverviewProcedureList from '$lib/components/ui/custom/overview_procedure_list.svelte';
	import OverviewTestResults from '$lib/components/ui/custom/overview_test_results.svelte';

	import OverviewStats from '$lib/components/ui/custom/overview_stats.svelte';
	import OverviewEnvironmentFilter from '$lib/components/ui/custom/overview_environment_filter.svelte';
	import OverviewPilotDateFilter from '$lib/components/ui/custom/overview_pilot_date_filter.svelte';
	import OverviewProcedureTests from '$lib/components/ui/custom/overview_procedure_tests.svelte';
	import OverviewSoftwareFilters from '$lib/components/ui/custom/overview_software_filters.svelte';

	import { importantProcedureIds } from '$lib/important-procedures';
	import { fetchUsers } from '$lib/api';
	import { users } from '$lib/stores';

	let selectedPx4Version: string = 'all';
	let selectedGliderCompanionVersion: string = 'all';
	let selectedQGroundControlVersion: string = 'all';
	let selectedReferenceParametersVersion: string = 'all';
	let selectedForceSensorVersion: string = 'all';
	let selectedLandingStationMercuryVersion: string = 'all';
	let selectedRAutopilotFTSVersion: string = 'all';
	let selectedFTSCommsServerVersion: string = 'all';
	let selectedFTSTriggerAndroidAppVersion: string = 'all';
	let selectedEnvironment: string = 'all';
	let selectedPilot: string = 'all';
	let selectedYear: string = 'all';
	let selectedMonth: string = 'all';


	let allTests: Test[] = [];
	let px4Versions: SoftwareVersion[] = [];
	let gliderCompanionVersions: SoftwareVersion[] = [];
	let qgroundcontrolVersions: string[] = [];
	let referenceParametersVersions: string[] = [];
	let forceSensorVersions: string[] = [];
	let landingStationMercuryVersions: string[] = [];
	let rAutopilotFTSVersions: string[] = [];
	let ftsCommsServerVersions: string[] = [];
	let ftsTriggerAndroidAppVersions: string[] = [];
	let filteredTests: Test[] = [];
	let availableYears: string[] = [];
	let availableMonths: string[] = [];
	let selectedProcedure: TestProcedure | null = null;
	let proceduresWithResults: Array<{
		procedure: TestProcedure;
		results: {
			total: number;
			passed: number;
			failed: number;
			pending: number;
		};
		isImportant: boolean;
	}> = [];
	let nonObsoleteProcedures: TestProcedure[] = [];

	async function loadAllTests() {
		try {
			// First fetch all procedures and filter out obsolete ones
			const allProcedures = await fetchTestProcedures();
			nonObsoleteProcedures = allProcedures.filter(proc => !proc.obsolete);

			// Get the IDs of non-obsolete procedures
			const nonObsoleteProcedureIds = new Set(nonObsoleteProcedures.map(proc => proc.id));

			// Fetch all tests and filter to only include those with non-obsolete procedure IDs
			const allTestsFromAPI = await fetchTests();
			allTests = allTestsFromAPI.filter(test => nonObsoleteProcedureIds.has(test.test_procedure_id));
		} catch (error) {
			console.error('Failed to fetch tests:', error);
		}
	}

	function sortVersions(versions: SoftwareVersion[]): SoftwareVersion[] {
		return [...versions].sort((a, b) => {
			const aParts = a.name.split('.').map((p) => parseInt(p) || 0);
			const bParts = b.name.split('.').map((p) => parseInt(p) || 0);

			for (let i = 0; i < Math.max(aParts.length, bParts.length); i++) {
				const aVal = aParts[i] || 0;
				const bVal = bParts[i] || 0;
				if (aVal !== bVal) {
					return bVal - aVal;
				}
			}
			return 0;
		});
	}

	async function loadVersions() {
		try {
			const rawPx4Versions = await fetchPx4Versions();
			const rawGCVersions = await fetchGliderCompanionVersions();

			px4Versions = sortVersions(rawPx4Versions);
			gliderCompanionVersions = sortVersions(rawGCVersions);
		} catch (error) {
			console.error('Failed to fetch versions:', error);
		}
	}

	function filterTestsByVersions(tests: Test[]): Test[] {
		const uniquePx4Versions = new Set();
		const uniqueGCVersions = new Set();
		tests.forEach((test) => {
			if (test.autopilot_software_version) uniquePx4Versions.add(test.autopilot_software_version);
			if (test.jetson_software_version) uniqueGCVersions.add(test.jetson_software_version);
		});

		const filteredTests = tests.filter((test) => {
			// PX4 Version filter
			let px4Match = true;
			if (selectedPx4Version === 'all') {
				px4Match = true;
			} else if (selectedPx4Version === 'no_version') {
				px4Match =
					test.autopilot_software_version === null ||
					test.autopilot_software_version === undefined ||
					test.autopilot_software_version === '';
			} else {
				const testVersionString = String(test.autopilot_software_version || '').trim();
				if (testVersionString) {
					const testParsed = parseVersion(testVersionString);
					px4Match = testParsed.baseVersion === selectedPx4Version;
				} else {
					px4Match = false;
				}
			}

			// Glider Companion Version filter
			let gcMatch = true;
			if (selectedGliderCompanionVersion === 'all') {
				gcMatch = true;
			} else if (selectedGliderCompanionVersion === 'no_version') {
				gcMatch =
					test.jetson_software_version === null ||
					test.jetson_software_version === undefined ||
					test.jetson_software_version === '';
			} else {
				const testVersionString = String(test.jetson_software_version || '').trim();
				if (testVersionString) {
					const testParsed = parseVersion(testVersionString);
					gcMatch = testParsed.baseVersion === selectedGliderCompanionVersion;
				} else {
					gcMatch = false;
				}
			}

			const softwareFilters = [
				{ selected: selectedQGroundControlVersion, field: 'qgroundcontrol_version' },
				{ selected: selectedReferenceParametersVersion, field: 'reference_parameters_version' },
				{ selected: selectedForceSensorVersion, field: 'force_sensor_version' },
				{
					selected: selectedLandingStationMercuryVersion,
					field: 'landing_station_mercury_version'
				},
				{ selected: selectedRAutopilotFTSVersion, field: 'r_autopilot_fts_version' },
				{ selected: selectedFTSCommsServerVersion, field: 'fts_comms_server_version' },
				{ selected: selectedFTSTriggerAndroidAppVersion, field: 'fts_trigger_android_app_version' }
			];

			for (const filter of softwareFilters) {
				if (filter.selected !== 'all') {
					const fieldValue = (test as any)[filter.field];
					if (filter.selected === 'no_version') {
						if (fieldValue && fieldValue !== 'not_tracked') return false;
					} else {
						if (fieldValue !== filter.selected) return false;
					}
				}
			}

			return px4Match && gcMatch;
		});

		return filteredTests;
	}

	let mounted = false;

	onMount(async () => {
		mounted = true;
		try {
			await Promise.all([fetchTestProcedures(), loadAllTests(), loadVersions(), fetchUsers()]);
			if (mounted) {
				updateFilteredData();
			}
		} catch (error) {
			console.error('Error loading initial data:', error);
		}
	});

	onDestroy(() => {
		mounted = false;
	});

	function handleSoftwareFilterChange(event: CustomEvent) {
		const { type, value } = event.detail;
		switch (type) {
			case 'px4':
				selectedPx4Version = value;
				break;
			case 'gc':
				selectedGliderCompanionVersion = value;
				break;
			case 'qgc':
				selectedQGroundControlVersion = value;
				break;
			case 'ref_params':
				selectedReferenceParametersVersion = value;
				break;
			case 'force_sensor':
				selectedForceSensorVersion = value;
				break;
			case 'landing_mercury':
				selectedLandingStationMercuryVersion = value;
				break;
			case 'r_auto_fts':
				selectedRAutopilotFTSVersion = value;
				break;
			case 'fts_comms':
				selectedFTSCommsServerVersion = value;
				break;
			case 'fts_android':
				selectedFTSTriggerAndroidAppVersion = value;
				break;
		}
		updateFilteredData();
	}

	$: if (
		selectedPx4Version ||
		selectedGliderCompanionVersion ||
		selectedEnvironment ||
		selectedPilot ||
		selectedYear ||
		selectedMonth
	) {
		updateFilteredData();
	}



	function handleProcedureSelected(event: CustomEvent) {
		selectedProcedure = event.detail.procedure;
	}

	function updateFilteredData() {
		if (allTests.length > 0 && nonObsoleteProcedures.length > 0) {
			const uniquePx4Versions = new Set();
			const uniqueGCVersions = new Set();
			const uniqueQGCVersions = new Set();
			const uniqueRefParamsVersions = new Set();
			const uniqueForceSensorVersions = new Set();
			const uniqueLandingMercuryVersions = new Set();
			const uniqueRAutoFTSVersions = new Set();
			const uniqueFTSCommsVersions = new Set();
			const uniqueFTSAndroidVersions = new Set();
			const uniqueYears = new Set<string>();
			const uniqueMonths = new Set<string>();

			allTests.forEach((test) => {
				if (test.autopilot_software_version) {
					uniquePx4Versions.add(String(test.autopilot_software_version).trim());
				}
				if (test.jetson_software_version) {
					uniqueGCVersions.add(String(test.jetson_software_version).trim());
				}
				if (test.qgroundcontrol_version) {
					uniqueQGCVersions.add(String(test.qgroundcontrol_version).trim());
				}
				if (test.reference_parameters_version) {
					uniqueRefParamsVersions.add(String(test.reference_parameters_version).trim());
				}
				if (test.force_sensor_version) {
					uniqueForceSensorVersions.add(String(test.force_sensor_version).trim());
				}
				if (test.landing_station_mercury_version) {
					uniqueLandingMercuryVersions.add(String(test.landing_station_mercury_version).trim());
				}
				if (test.r_autopilot_fts_version) {
					uniqueRAutoFTSVersions.add(String(test.r_autopilot_fts_version).trim());
				}
				if (test.fts_comms_server_version) {
					uniqueFTSCommsVersions.add(String(test.fts_comms_server_version).trim());
				}
				if (test.fts_trigger_android_app_version) {
					uniqueFTSAndroidVersions.add(String(test.fts_trigger_android_app_version).trim());
				}

				if (test.date) {
					const testDate = new Date(test.date);
					uniqueYears.add(testDate.getFullYear().toString());
					uniqueMonths.add(testDate.toLocaleString('default', { month: 'long' }));
				}
			});

			// Оновлюємо списки версій
			qgroundcontrolVersions = Array.from(uniqueQGCVersions) as string[];
			qgroundcontrolVersions.sort();
			referenceParametersVersions = Array.from(uniqueRefParamsVersions) as string[];
			referenceParametersVersions.sort();
			forceSensorVersions = Array.from(uniqueForceSensorVersions) as string[];
			forceSensorVersions.sort();
			landingStationMercuryVersions = Array.from(uniqueLandingMercuryVersions) as string[];
			landingStationMercuryVersions.sort();
			rAutopilotFTSVersions = Array.from(uniqueRAutoFTSVersions) as string[];
			rAutopilotFTSVersions.sort();
			ftsCommsServerVersions = Array.from(uniqueFTSCommsVersions) as string[];
			ftsCommsServerVersions.sort();
			ftsTriggerAndroidAppVersions = Array.from(uniqueFTSAndroidVersions) as string[];
			ftsTriggerAndroidAppVersions.sort();

			availableYears = Array.from(uniqueYears).sort((a, b) => parseInt(b) - parseInt(a));
			availableMonths = [
				'January', 'February', 'March', 'April', 'May', 'June',
				'July', 'August', 'September', 'October', 'November', 'December'
			];
		}

		let versionFilteredTests = filterTestsByVersions(allTests);

		if (selectedEnvironment !== 'all') {
			versionFilteredTests = versionFilteredTests.filter((test) => {
				const procedure = nonObsoleteProcedures.find((p) => p.id === test.test_procedure_id);

				switch (selectedEnvironment) {
					case 'sim':
						return test.simulation === true;
					case 'irl':
						return test.simulation === false;
					case 'hil':
						return procedure?.hil_test === true;
					case 'sim_hil':
						return test.simulation === true && procedure?.hil_test === true;
					case 'irl_hil':
						return test.simulation === false && procedure?.hil_test === true;
					case 'no_hil':
						return procedure?.hil_test === false;
					default:
						return true;
				}
			});
		}

		if (selectedPilot !== 'all') {
			versionFilteredTests = versionFilteredTests.filter(
				(test) => test.tester_id === selectedPilot
			);
		}

		if (selectedYear !== 'all') {
			versionFilteredTests = versionFilteredTests.filter((test) => {
				if (test.date) {
					const testDate = new Date(test.date);
					return testDate.getFullYear().toString() === selectedYear;
				}
				return false;
			});
		}

		if (selectedMonth !== 'all') {
			versionFilteredTests = versionFilteredTests.filter((test) => {
				if (test.date) {
					const testDate = new Date(test.date);
					const testMonth = testDate.toLocaleString('default', { month: 'long' });
					return testMonth === selectedMonth;
				}
				return false;
			});
		}

		filteredTests = versionFilteredTests;

		// Use non-obsolete procedures and get the latest version of each
		const latestProcedures = new Map<string, TestProcedure>();
		nonObsoleteProcedures.forEach((proc) => {
			const existing = latestProcedures.get(proc.name);
			if (!existing || proc.version > existing.version) {
				latestProcedures.set(proc.name, proc);
			}
		});

		let filteredProcedures = Array.from(latestProcedures.values());

		if (selectedEnvironment !== 'all') {
			filteredProcedures = filteredProcedures.filter((proc) => {
				switch (selectedEnvironment) {
					case 'sim':
						return proc.simulation === true;
					case 'irl':
						return proc.field_test === true;
					case 'hil':
						return proc.hil_test === true;
					case 'sim_hil':
						return proc.simulation === true && proc.hil_test === true;
					case 'irl_hil':
						return proc.field_test === true && proc.hil_test === true;
					case 'no_hil':
						return proc.hil_test === false;
					default:
						return true;
				}
			});
		}

		let tempProceduresWithResults = filteredProcedures.map((proc) => {
			let testsForProcedure = filteredTests.filter(
				(test) => test.test_procedure_id === proc.id && !test.manually_cleared
			);

			const results = {
				total: testsForProcedure.length,
				passed: testsForProcedure.filter((test) => test.success_status === true).length,
				failed: testsForProcedure.filter((test) => test.success_status === false).length,
				pending: testsForProcedure.filter((test) => test.success_status === null).length
			};

			const isImportant = importantProcedureIds.includes(proc.external_id);
			return {
				procedure: proc,
				results: results,
				isImportant: isImportant
			};
		});

		proceduresWithResults = tempProceduresWithResults
			.sort((a, b) => {
				if (a.isImportant && !b.isImportant) return -1;
				if (!a.isImportant && b.isImportant) return 1;
				return a.procedure.external_id - b.procedure.external_id;
			});
	}
</script>

<div class="container mx-auto p-4">
	<h1 class="mb-4 text-2xl font-bold">Test Procedures Overview</h1>

	<div class="mb-4">
		<div class="grid grid-cols-1 gap-4 md:grid-cols-2">
			<div>
				<!-- Software Filters -->
				<OverviewSoftwareFilters
					{px4Versions}
					{gliderCompanionVersions}
					{qgroundcontrolVersions}
					{referenceParametersVersions}
					{forceSensorVersions}
					{landingStationMercuryVersions}
					{rAutopilotFTSVersions}
					{ftsCommsServerVersions}
					{ftsTriggerAndroidAppVersions}
					bind:selectedPx4Version
					bind:selectedGliderCompanionVersion
					bind:selectedQGroundControlVersion
					bind:selectedReferenceParametersVersion
					bind:selectedForceSensorVersion
					bind:selectedLandingStationMercuryVersion
					bind:selectedRAutopilotFTSVersion
					bind:selectedFTSCommsServerVersion
					bind:selectedFTSTriggerAndroidAppVersion
					testType={selectedEnvironment === 'sim'
						? 'sim'
						: selectedEnvironment === 'irl' || selectedEnvironment === 'hil'
							? 'ft'
							: 'all'}
					on:change={handleSoftwareFilterChange}
				/>
			</div>

			<div>
				<OverviewEnvironmentFilter
					bind:selectedEnvironment
					on:change={() => updateFilteredData()}
				/>

				<div class="mt-4">
					<OverviewPilotDateFilter
						pilots={$users}
						{availableYears}
						{availableMonths}
						bind:selectedPilot
						bind:selectedYear
						bind:selectedMonth
						on:change={() => updateFilteredData()}
					/>
				</div>
			</div>
		</div>
	</div>

	<OverviewStats procedures={proceduresWithResults} />

	<div class="grid grid-cols-1 gap-4 md:grid-cols-12">
		<div class="md:col-span-5">
			<Card.Root>
				<Card.Header>
					<Card.Title>Procedures</Card.Title>
					<Card.Description>List of test procedures</Card.Description>
				</Card.Header>
				<Card.Content>
					<ScrollArea class="h-[600px]">
						<OverviewProcedureList
							procedures={proceduresWithResults}
							on:procedureSelected={handleProcedureSelected}
						/>
					</ScrollArea>
				</Card.Content>
			</Card.Root>
		</div>
		<div class="md:col-span-7">
			{#if selectedProcedure}
				<Card.Root>
					<Card.Header>
						<Card.Title>
							Procedure Tests
							<button
								class="ml-2 text-sm text-muted-foreground hover:text-foreground"
								on:click={() => (selectedProcedure = null)}
							>
								← Back to overview
							</button>
						</Card.Title>
						<Card.Description>Tests for {selectedProcedure.name}</Card.Description>
					</Card.Header>
					<Card.Content>
						<OverviewProcedureTests {selectedProcedure} tests={filteredTests} />
					</Card.Content>
				</Card.Root>
			{:else}
				<Card.Root>
					<Card.Header>
						<Card.Title>Test Results</Card.Title>
						<Card.Description>
							Pass/Fail statistics for each procedure
							{#if selectedPx4Version !== 'all' || selectedGliderCompanionVersion !== 'all' || selectedEnvironment !== 'all' || selectedPilot !== 'all' || selectedYear !== 'all' || selectedMonth !== 'all'}
								<span class="ml-2">
									{#if selectedPx4Version !== 'all'}
										<Badge variant="outline"
											>PX4: {selectedPx4Version === 'no_version'
												? 'No Version'
												: selectedPx4Version}</Badge
										>
									{/if}
									{#if selectedGliderCompanionVersion !== 'all'}
										<Badge variant="outline"
											>Glider Companion: {selectedGliderCompanionVersion === 'no_version'
												? 'No Version'
												: selectedGliderCompanionVersion}</Badge
										>
									{/if}
									{#if selectedEnvironment !== 'all'}
										<Badge variant="outline">
											{#if selectedEnvironment === 'sim'}
												SIM
											{:else if selectedEnvironment === 'irl'}
												FT
											{:else if selectedEnvironment === 'hil'}
												HIL Only
											{:else if selectedEnvironment === 'sim_hil'}
												SIM + HIL
											{:else if selectedEnvironment === 'irl_hil'}
												FT + HIL
											{:else if selectedEnvironment === 'no_hil'}
												No HIL
											{/if}
										</Badge>
									{/if}
									{#if selectedPilot !== 'all'}
										<Badge variant="outline"
											>Pilot: {$users.find((u) => u.id === selectedPilot)?.email ||
												selectedPilot}</Badge
										>
									{/if}
									{#if selectedYear !== 'all'}
										<Badge variant="outline">Year: {selectedYear}</Badge>
									{/if}
									{#if selectedMonth !== 'all'}
										<Badge variant="outline">Month: {selectedMonth}</Badge>
									{/if}
								</span>
							{/if}
						</Card.Description>
					</Card.Header>
					<Card.Content>
						<ScrollArea class="h-[600px]">
							<OverviewTestResults procedures={proceduresWithResults} />
						</ScrollArea>
					</Card.Content>
				</Card.Root>
			{/if}
		</div>
	</div>
</div>
