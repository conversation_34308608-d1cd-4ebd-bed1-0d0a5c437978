<script lang="ts">
	import { FlaskConical } from 'lucide-svelte/icons';
	import * as Avatar from '$lib/components/ui/avatar';
	import * as HoverCard from '$lib/components/ui/hover-card';
	import { goto } from '$app/navigation';
	import '../app.css';

	import Keycloak from 'keycloak-js';
	import type { KeycloakInitOptions } from 'keycloak-js';
	import { onMount } from 'svelte';
	import { userProfile, keycloakClient } from '$lib/stores';
	import * as Tooltip from '$lib/components/ui/tooltip';
	import FileSpreadsheet from 'lucide-svelte/icons/file-spreadsheet';
	import { Button } from '$lib/components/ui/button';
	import { exportCSV } from '$lib/api';
	import environment from '$lib/environment';

	let keycloak_initialized = false;
	onMount(async () => {
		let instance = {
			url: environment.authUrl,
			realm: environment.authRealm,
			clientId: environment.authClientId
		};

		let keycloak = new Keycloak(instance);
		$keycloakClient = keycloak;
		const token = localStorage.getItem('kc_token');
		const refreshToken = localStorage.getItem('kc_refreshToken');

		let initOptions: KeycloakInitOptions = {
			onLoad: 'login-required',
			checkLoginIframe: false,
			token: token,
			refreshToken: refreshToken,
			// Reduce history API conflicts with SvelteKit
			flow: 'standard',
			responseMode: 'fragment'
		};

		keycloak
			.init(initOptions)
			.then((authenticated) => {
				if (authenticated) {
					localStorage.setItem('kc_token', keycloak.token ?? '');
					localStorage.setItem('kc_refreshToken', keycloak.refreshToken ?? '');
					keycloak
						.loadUserProfile()
						.then((profile) => {
							$userProfile = profile;
							console.log($userProfile);
						})
						.catch((error) => {
							console.error('Failed to load user profile', error);
						});
					keycloak_initialized = true;

					return;
				}
			})
			.catch((error) => {
				console.error('Keycloak initialization failed:', error);
			});

		keycloak.onTokenExpired = async () => {
			keycloak.updateToken(30);
		};
	});

	let disabled_csv_button = false;

	const download_csv = async () => {
		disabled_csv_button = true;
		try {
			await exportCSV();
		} catch (error) {
			console.error('Failed to export CSV:', error);
		} finally {
			disabled_csv_button = false;
		}
	};
</script>

<div>
	<nav class="flex items-center justify-between bg-primary p-3 text-background">
		<div class="flex font-bold">
			<FlaskConical class="mx-2 h-6 w-6" />
			<div class="flex space-x-4">
				<a href="/" class="hover:text-gray-200">Flight Tests</a>
			</div>
		</div>
		<div class="flex font-light">
			{#if $userProfile}
				<Tooltip.Root openDelay={200}>
					<Tooltip.Trigger>
						<Button
							variant="ghost"
							class="relative mr-5 h-8 w-8 p-0"
							on:click={download_csv}
							disabled={disabled_csv_button}
						>
							<FileSpreadsheet class="h-6 w-6" />
						</Button>
					</Tooltip.Trigger>
					<Tooltip.Content>
						<p>Export CSV</p>
					</Tooltip.Content>
				</Tooltip.Root>
				<Tooltip.Root openDelay={200}>
					<Tooltip.Trigger>
						<a href="/overview" class="mr-5 hover:text-gray-200">
							<Button variant="ghost" class="relative h-8 w-8 p-0">
								<svg
									xmlns="http://www.w3.org/2000/svg"
									width="24"
									height="24"
									viewBox="0 0 24 24"
									fill="none"
									stroke="currentColor"
									stroke-width="2"
									stroke-linecap="round"
									stroke-linejoin="round"
									class="h-6 w-6"
									><rect width="18" height="18" x="3" y="3" rx="2" /><path d="M9 9h6v6H9z" /><path
										d="M6 6h.01"
									/><path d="M6 12h.01" /><path d="M6 18h.01" /><path d="M12 6h.01" /><path
										d="M12 18h.01"
									/><path d="M18 6h.01" /><path d="M18 12h.01" /><path d="M18 18h.01" /></svg
								>
							</Button>
						</a>
					</Tooltip.Trigger>
					<Tooltip.Content>
						<p>Overview</p>
					</Tooltip.Content>
				</Tooltip.Root>
				<HoverCard.Root>
					<HoverCard.Trigger
						><Avatar.Root class="h-8 w-8 text-sm text-black">
							<Avatar.Fallback
								>{($userProfile?.firstName ?? ' ')[0] +
									($userProfile?.lastName ?? ' ')[0]}</Avatar.Fallback
							>
						</Avatar.Root></HoverCard.Trigger
					>
					<HoverCard.Content>
						Logged in as {$userProfile?.firstName}
						{$userProfile?.lastName}. All the tests that you report or trigger will be associated
						with this account.
					</HoverCard.Content>
				</HoverCard.Root>
			{/if}
		</div>
	</nav>
</div>
<div class="m-5">
	{#if keycloak_initialized}
		<slot></slot>
	{:else}
		<div class="flex h-screen items-center justify-center">
			<div class="h-32 w-32 animate-spin rounded-full border-b-2 border-t-2 border-primary"></div>
		</div>
	{/if}
</div>
