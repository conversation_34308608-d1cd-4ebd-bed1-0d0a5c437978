<script lang="ts">
	import * as Card from '$lib/components/ui/card';
	import ProcedureCard from '$lib/components/ui/custom/procedure_card.svelte';
	import ProcedureDetails from '$lib/components/ui/custom/procedure_details.svelte';
	import ProcedureFilter from '$lib/components/ui/custom/procedure_filter.svelte';
	import { ScrollArea } from '$lib/components/ui/scroll-area/index.js';
	import type { TestProcedure } from '$lib/types';
	import { selectedTestProcedure } from '$lib/stores';
	import { buttonVariants } from '$lib/components/ui/button/index.js';
	import { Badge } from '$lib/components/ui/badge';

	import { Button } from '$lib/components/ui/button/index.js';

	import { get_procedure_last_test_date } from '$lib/utils';

	import { CirclePlus, TestTube2, Pencil, Trash2 } from 'lucide-svelte';
	import * as Dialog from '$lib/components/ui/dialog';
	import * as Tabs from '$lib/components/ui/tabs';

	import {
		fetchTestProcedures,
		fetchGroups,
		fetchEnvironments,
		fetchTestTypes,
		fetchVtolModes,
		fetchSimWorlds,
		fetchWindModes,
		fetchAutopilotModes,
		fetchGliders,
		fetchUsers,
		worker_available,
		getLastTestPerProcedure,
		deleteTestProcedure,
		fetchTags
	} from '$lib/api';
	import { onMount, onDestroy } from 'svelte';
	import {
		testProcedures,
		testProcedureGroups,
		testProcedureEnvironmentNames,
		testProcedureTypeNames,
		VTOLModes,
		autopilotModes,
		simulationWorlds,
		windModes,
		gliders,
		users,
		simRunnerOnline,
		testProceduresMaxVersion,
		testDates,
		tags
	} from '$lib/stores';
	import TriggerAutoTestForm from '$lib/components/ui/custom/trigger_auto_test_form.svelte';
	import ReportManualTestForm from '$lib/components/ui/custom/report_manual_test_form.svelte';
	import * as Tooltip from '$lib/components/ui/tooltip';
	import RegisterProcedureForm from '$lib/components/ui/custom/register_procedure_form.svelte';

	let sim_runner_online_interval: number;

	onMount(async () => {
		$testProcedures = (await fetchTestProcedures()) as TestProcedure[];
		let proc: TestProcedure;
		for (proc of $testProcedures) {
			if (
				$testProceduresMaxVersion[proc.name] == undefined ||
				proc.version > $testProceduresMaxVersion[proc.name]
			) {
				$testProceduresMaxVersion[proc.name] = proc.version;
			}
		}
		$testProcedureGroups = await fetchGroups();
		$testProcedureEnvironmentNames = await fetchEnvironments();
		$testProcedureTypeNames = await fetchTestTypes();
		$VTOLModes = await fetchVtolModes();
		$autopilotModes = await fetchAutopilotModes();
		$tags = await fetchTags();
		$simulationWorlds = await fetchSimWorlds();
		$windModes = await fetchWindModes();
		$gliders = await fetchGliders();
		$users = await fetchUsers();
		$simRunnerOnline = await worker_available();
		$testDates = await getLastTestPerProcedure();

		sim_runner_online_interval = setInterval(async () => {
			$simRunnerOnline = await worker_available();
		}, 10000);
	});

	onDestroy(() => {
		clearInterval(sim_runner_online_interval);
	});

	let testProcedureEnvironmentFilter: string | null = null;
	let testProcedureTypeFilter: string | null = null;
	let testProcedureGroupNameFilter: string = '';
	let show_obsolete = false;

	const match_environment = (x: TestProcedure, env: string) => {
		if (env === 'Any') return true;
		return (
			(env === 'SIM' && x.simulation) ||
			(env === 'HIL' && x.hil_test) ||
			(env === 'IRL' && x.field_test)
		);
	};

	let new_test_dialog_open = false;
	let selectedTagFilter: string | null = null;
</script>

<div class="flex h-full">
	<div id="procedures_list" class="m-1 flex w-2/5 flex-col">
		<Card.Root class="flex flex-shrink flex-grow flex-col">
			<Card.Header>
				<div class="flex justify-between align-middle">
					<div>
						<Card.Title>Procedures</Card.Title>
						<Card.Description
							>Choose a procedure to show its test history on the right panel</Card.Description
						>
					</div>

					<Dialog.Root closeOnOutsideClick={false}>
						<Tooltip.Root openDelay={200}>
							<Tooltip.Trigger>
								<Dialog.Trigger class={buttonVariants({ variant: 'ghost' })}>
									<CirclePlus class="hover:cursor-pointer hover:text-gray-800 hover:shadow-2xl" />
								</Dialog.Trigger>
							</Tooltip.Trigger>
							<Tooltip.Content><p>Register a new test procedure</p></Tooltip.Content>
						</Tooltip.Root>
						<Dialog.Content class="max-w-screen-2xl">
							<Dialog.Header>
								<Dialog.Title>Register a new test procedure</Dialog.Title>
							</Dialog.Header>
							<Dialog.Description>
								<ScrollArea class="h-[80vh] w-full">
									<RegisterProcedureForm />
								</ScrollArea>
							</Dialog.Description>
						</Dialog.Content>
					</Dialog.Root>
				</div>
			</Card.Header>
			<Card.Content class="flex-shrink flex-grow overflow-hidden">
				<ProcedureFilter
					bind:selectedEnvironment={testProcedureEnvironmentFilter}
					bind:selectedType={testProcedureTypeFilter}
					bind:selectedGroup={testProcedureGroupNameFilter}
					bind:show_obsolete
					bind:selectedTag={selectedTagFilter}
					bind:tags={$tags}
				/>
				<!-- TODO: Fix the scrollable container height, and the above mess with flex-col -->
				<ScrollArea class="h-[600px]">
					{#each $testProcedures
						.filter((x) => testProcedureTypeFilter === null || x.test_type === testProcedureTypeFilter)
						.filter((x) => testProcedureEnvironmentFilter === null || match_environment(x, testProcedureEnvironmentFilter))
						.filter((x) => testProcedureGroupNameFilter === '' || x.group.name === testProcedureGroupNameFilter)
						.filter((x) => selectedTagFilter === null || x.tags.some((tag) => tag.display_name === selectedTagFilter))
						.filter((x) => !x.obsolete || show_obsolete) as data}
						<div
							on:click={() => {
								$selectedTestProcedure = data;
							}}
						>
							<ProcedureCard {data} />
						</div>
					{/each}
				</ScrollArea>
			</Card.Content>
		</Card.Root>
	</div>
	<div id="tests_list" class="m-1 w-3/5">
		<Card.Root>
			{#if $selectedTestProcedure !== null}
				<Card.Header>
					<Card.Title>
						<div class="flex justify-between">
							<p>
								{$selectedTestProcedure?.name} (v{$selectedTestProcedure?.version}) {#if $selectedTestProcedure?.obsolete}
									<Badge variant="outline">OBSOLETE</Badge>
								{/if}
							</p>
							<div>
								{#if !$selectedTestProcedure?.obsolete}
									{#key $testDates}
										{#if $testDates.length && get_procedure_last_test_date($selectedTestProcedure) === 'Never Tested'}
											<span>
												<Dialog.Root>
													<Tooltip.Root openDelay={100}>
														<Tooltip.Trigger>
															<Dialog.Trigger class={buttonVariants({ variant: 'ghost' })}>
																<Trash2 class="text-destructive hover:cursor-pointer" />
															</Dialog.Trigger>
														</Tooltip.Trigger>
														<Tooltip.Content><p>Delete test procedure</p></Tooltip.Content>
													</Tooltip.Root>
													<Dialog.Content>
														<Dialog.Header>
															<Dialog.Title>Delete this test procedure?</Dialog.Title>
														</Dialog.Header>
														<Dialog.Description>
															Are you sure you want to delete the test procedure: {$selectedTestProcedure.name}
															v{$selectedTestProcedure.version}?
														</Dialog.Description>
														<Dialog.Footer>
															<Dialog.Close
																><Button variant="secondary">Cancel</Button></Dialog.Close
															>
															<Dialog.Close
																><Button
																	on:click={() => {
																		deleteTestProcedure($selectedTestProcedure?.id);
																		location.reload();
																	}}>Delete</Button
																></Dialog.Close
															>
														</Dialog.Footer>
													</Dialog.Content>
												</Dialog.Root>
											</span>
										{/if}
									{/key}
									<span>
										<Dialog.Root closeOnOutsideClick={false}>
											<Tooltip.Root openDelay={200}>
												<Tooltip.Trigger>
													<Dialog.Trigger class={buttonVariants({ variant: 'ghost' })}>
														<Pencil
															class="text-gray-700 hover:cursor-pointer hover:text-gray-800 hover:shadow-2xl"
														/>
													</Dialog.Trigger>
												</Tooltip.Trigger>
												<Tooltip.Content><p>Edit test procedure</p></Tooltip.Content>
											</Tooltip.Root>
											<Dialog.Content class="max-w-screen-2xl">
												<Dialog.Header>
													<Dialog.Title>Edit test procedure</Dialog.Title>
												</Dialog.Header>
												<Dialog.Description>
													<ScrollArea class="h-[80vh] w-full">
														<RegisterProcedureForm edit={true} />
													</ScrollArea>
												</Dialog.Description>
											</Dialog.Content>
										</Dialog.Root>
									</span>
								{/if}
								<Dialog.Root bind:open={new_test_dialog_open}>
									<Dialog.Trigger class={buttonVariants({ variant: 'outline' })}>
										<TestTube2 class="mr-2 h-6 w-6" />
										New Test
									</Dialog.Trigger>
									<Dialog.Content>
										<Dialog.Header>
											<Dialog.Title>Add a New Test</Dialog.Title>
										</Dialog.Header>
										<Dialog.Description>
											{#if $selectedTestProcedure.automated}
												<Tabs.Root value="history" class="mt-5 w-full">
													<Tabs.List class="w-full">
														<Tabs.Trigger value="history" class="w-1/2"
															>Trigger an automated test</Tabs.Trigger
														>
														<Tabs.Trigger value="setup" class="w-1/2"
															>Report a manual test</Tabs.Trigger
														>
													</Tabs.List>
													<Tabs.Content value="history">
														<TriggerAutoTestForm bind:dialog_open={new_test_dialog_open} />
													</Tabs.Content>
													<Tabs.Content value="setup">
														<ReportManualTestForm bind:dialog_open={new_test_dialog_open} />
													</Tabs.Content>
												</Tabs.Root>
											{:else}
												<ReportManualTestForm bind:dialog_open={new_test_dialog_open} />
											{/if}
										</Dialog.Description>
									</Dialog.Content>
								</Dialog.Root>
							</div>
						</div>
					</Card.Title>
					<Card.Description></Card.Description>
				</Card.Header>
				<Card.Content>
					<ProcedureDetails />
				</Card.Content>
			{:else}
				<Card.Header>
					<Card.Description
						>Selected procedure details and history will appear here</Card.Description
					>
				</Card.Header>
			{/if}
		</Card.Root>
	</div>
</div>
