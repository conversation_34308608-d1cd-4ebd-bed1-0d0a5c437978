<script lang="ts">
	import { Badge } from '$lib/components/ui/badge';
	import * as Tooltip from '$lib/components/ui/tooltip';
	import { FlagTriangleRight } from 'lucide-svelte';
	import { cn } from '$lib/utils';
	import * as Tabs from '$lib/components/ui/tabs';
	import type { Test } from '$lib/types';
	import { Separator } from '$lib/components/ui/separator/index.js';
	import TestHistory from '$lib/components/ui/custom/test_history.svelte';
	import ScrollArea from '../scroll-area/scroll-area.svelte';
	import { selectedTestProcedure, gliders, users } from '$lib/stores';
	import { onDestroy, onMount } from 'svelte';
	import { fetchTests } from '$lib/api';

	let history: Test[] | null = null;

	let interval: number;

	onMount(async () => {
		history = await fetchTests(0, 5000, $selectedTestProcedure?.id);

		interval = setInterval(async () => {
			history = await fetchTests(0, 5000, $selectedTestProcedure?.id);
		}, 3000);
	});

	onDestroy(() => {
		clearInterval(interval);
	});

	const priority_flag_color = (priority_level: string | undefined): string => {
		console.log('Priority level: ', priority_level);
		if (priority_level == 'high') {
			return 'text-orange-600';
		} else if (priority_level == 'medium') {
			return 'text-yellow-600';
		} else {
			return 'text-green-600';
		}
	};

	function capitalizeFirstLetter(string: string): string {
		return string.charAt(0).toUpperCase() + string.slice(1);
	}

	let selected_tab = 'history';
</script>

{#if $selectedTestProcedure?.obsolete}
	<p class="mb-3 text-sm italic">
		This test procedure is obsolete and should not be used for new tests.
	</p>
{:else if $selectedTestProcedure?.version > 1}
	<p class="mb-3 text-sm italic">
		There are older versions of this test procedure that are hidden. Their test history would still
		be exported in CSV.
	</p>
{/if}
<div>
	<div class="flex justify-between">
		<div class="flex justify-start">
			<p class="text-sm text-muted-foreground">
				{$selectedTestProcedure?.group.name} - {$selectedTestProcedure?.test_type}
			</p>
		</div>
		<div class="flex items-center justify-end">
			<Tooltip.Root openDelay={200}>
				<Tooltip.Trigger>
					<FlagTriangleRight
						class={cn('mr-1 h-5 w-5', priority_flag_color($selectedTestProcedure?.priority_level))}
					/>
				</Tooltip.Trigger>
				<Tooltip.Content>
					<p>Priority: {capitalizeFirstLetter($selectedTestProcedure?.priority_level ?? '')}</p>
				</Tooltip.Content>
			</Tooltip.Root>
			{#if $selectedTestProcedure?.simulation}
				<Badge variant="default" class="mr-1">SIM</Badge>
			{/if}
			{#if $selectedTestProcedure?.field_test}
				<Badge variant="default" class="mr-1">FT</Badge>
			{/if}
			{#if $selectedTestProcedure?.hil_test}
				<Badge variant="default" class="mr-1">HIL</Badge>
			{/if}
			{#if $selectedTestProcedure?.qgroundcontrol}
				<Badge variant="default" class="mr-1">QGC</Badge>
			{/if}
			{#if $selectedTestProcedure?.mdp}
				<Badge variant="default" class="mr-1">MDP</Badge>
			{/if}
			{#if $selectedTestProcedure?.precland_docker}
				<Badge variant="default" class="mr-1">Companion</Badge>
			{/if}
		</div>
	</div>
	<div class="mt-5 flex justify-between font-mono text-sm">
		<div class="mr-8 w-[35%] flex-grow">
			<div class="flex items-center justify-between">
				<p>Simulation World</p>
				<div class="mx-1 flex-grow">
					<Separator />
				</div>
				<p>{$selectedTestProcedure?.sim_world}</p>
			</div>
			<div class="flex items-center justify-between">
				<p>Mission Plan</p>
				<div class="mx-1 flex-grow">
					<Separator />
				</div>
				<p>{$selectedTestProcedure?.mission_plan}</p>
			</div>
		</div>
		<div class="mx-8 w-[27%] flex-grow">
			<div class="flex items-center justify-between">
				<p>Autopilot mode</p>
				<div class="mx-1 flex-grow">
					<Separator />
				</div>
				<p>{$selectedTestProcedure?.autopilot_mode}</p>
			</div>
			<div class="flex items-center justify-between">
				<p>VTOL Mode</p>
				<div class="mx-1 flex-grow">
					<Separator />
				</div>
				<p>{$selectedTestProcedure?.vtol_mode}</p>
			</div>
		</div>
		<div class="ml-8 flex-grow">
			<div class="flex items-center justify-between">
				<p>Wind Mode</p>
				<div class="mx-1 flex-grow">
					<Separator />
				</div>
				<p>{$selectedTestProcedure?.wind_mode}</p>
			</div>
			<div class="flex items-center justify-between">
				<p>Wind speed</p>
				<div class="mx-1 flex-grow">
					<Separator />
				</div>
				<p>{$selectedTestProcedure?.wind_speed} m/s</p>
			</div>
		</div>
	</div>
</div>
{#key $selectedTestProcedure}
	<Tabs.Root bind:value={selected_tab} class="mt-5 w-full">
		<Tabs.List class="w-full">
			<Tabs.Trigger value="history" class="w-1/2">Test History</Tabs.Trigger>
			<Tabs.Trigger value="setup" class="w-1/2">Procedure Setup and Expectations</Tabs.Trigger>
		</Tabs.List>
		<Tabs.Content value="history">
			<ScrollArea class="h-[500px]">
				{#if history === null || $users.length === 0 || $gliders.length === 0}
					<div class="p-4 text-center">Loading...</div>
				{:else}
					{#key selectedTestProcedure}
						<TestHistory data={history} procedure_id={$selectedTestProcedure?.id} />
					{/key}
				{/if}
			</ScrollArea>
		</Tabs.Content>
		<Tabs.Content value="setup">
			<ScrollArea class="h-[500px]">
				{#if $selectedTestProcedure?.procedures_simulation && $selectedTestProcedure?.procedures_simulation.length > 0}
					<h1 class="mt-2 font-bold">Procedures for Simulation Test</h1>
					<ol class="my-1 ml-4 list-inside list-decimal">
						{#each $selectedTestProcedure?.procedures_simulation ?? [] as procedure}
							<li>{procedure}</li>
						{/each}
					</ol>
					<Separator class="mt-5" />
					<h1 class="mt-2 font-bold">Expectations for Simulation Test</h1>
					<ol class="my-1 ml-4 list-inside list-decimal">
						{#each $selectedTestProcedure?.expectations_simulation ?? [] as expectation}
							<li>{expectation}</li>
						{/each}
					</ol>
					<Separator class="mt-5" />
				{/if}
				{#if $selectedTestProcedure?.procedures_field && $selectedTestProcedure?.procedures_field.length > 0}
					<h1 class="mt-2 font-bold">Procedures for Field Test</h1>
					<ol class="my-1 ml-4 list-inside list-decimal">
						{#each $selectedTestProcedure?.procedures_field ?? [] as procedure}
							<li>{procedure}</li>
						{/each}
					</ol>
					<Separator class="mt-5" />
					<h1 class="mt-2 font-bold">Expectations for Field Test</h1>
					<ol class="my-1 ml-4 list-inside list-decimal">
						{#each $selectedTestProcedure?.expectations_field ?? [] as expectation}
							<li>{expectation}</li>
						{/each}
					</ol>
					<Separator class="mt-5" />
				{/if}
				{#if $selectedTestProcedure?.procedures_hil && $selectedTestProcedure?.procedures_hil.length > 0}
					<h1 class="mt-2 font-bold">Procedures for HIL Test</h1>
					<ol class="my-1 ml-4 list-inside list-decimal">
						{#each $selectedTestProcedure?.procedures_hil ?? [] as procedure}
							<li>{procedure}</li>
						{/each}
					</ol>
					<Separator class="mt-5" />
					<h1 class="mt-2 font-bold">Expectations for HIL Test</h1>
					<ol class="my-1 ml-4 list-inside list-decimal">
						{#each $selectedTestProcedure?.expectations_hil ?? [] as expectation}
							<li>{expectation}</li>
						{/each}
					</ol>
					<Separator class="mt-5" />
				{/if}
			</ScrollArea>
		</Tabs.Content>
	</Tabs.Root>
{/key}
