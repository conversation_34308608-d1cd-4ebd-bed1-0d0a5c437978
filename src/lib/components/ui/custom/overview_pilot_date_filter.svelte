<script lang="ts">
	import * as Select from '$lib/components/ui/select/index.js';
	import { Badge } from '$lib/components/ui/badge';
	import { createEventDispatcher } from 'svelte';
	import type { KeycloakProfile } from 'keycloak-js';

	export let pilots: KeycloakProfile[] = [];
	export let availableYears: string[] = [];
	export let availableMonths: string[] = [];
	export let selectedPilot: string = 'all';
	export let selectedYear: string = 'all';
	export let selectedMonth: string = 'all';

	const dispatch = createEventDispatcher();

	function handlePilotChange(value: string | undefined) {
		selectedPilot = value || 'all';
		dispatch('change', { type: 'pilot', value: selectedPilot });
	}

	function handleYearChange(value: string | undefined) {
		selectedYear = value || 'all';
		dispatch('change', { type: 'year', value: selectedYear });
	}

	function handleMonthChange(value: string | undefined) {
		selectedMonth = value || 'all';
		dispatch('change', { type: 'month', value: selectedMonth });
	}

	// Create pilot options with display names
	$: pilotOptions = [
		{ value: 'all', label: 'All' },
		...pilots.map((pilot) => ({
			value: pilot.id || '',
			label:
				pilot.email ||
				pilot.username ||
				(pilot.firstName && pilot.lastName ? `${pilot.firstName} ${pilot.lastName}` : 'Unknown')
		}))
	];

	// Create year options
	$: yearOptions = [
		{ value: 'all', label: 'All' },
		...availableYears.map((year) => ({ value: year, label: year }))
	];

	// Create month options
	$: monthOptions = [
		{ value: 'all', label: 'All' },
		...availableMonths.map((month) => ({ value: month, label: month }))
	];
</script>

<div>
	<div class="grid grid-cols-1 gap-4 md:grid-cols-3">
		<!-- Pilot Filter -->
		<div>
			<h3 class="text-md mb-1 font-medium">Pilot</h3>
			<Select.Root
				selected={{
					value: selectedPilot,
					label: pilotOptions.find((p) => p.value === selectedPilot)?.label || 'All'
				}}
				onSelectedChange={(selected) => handlePilotChange(selected?.value)}
			>
				<Select.Trigger class="w-full">
					<Select.Value placeholder="Select pilot" />
				</Select.Trigger>
				<Select.Content>
					{#each pilotOptions as option}
						<Select.Item value={option.value}>
							{option.label}
						</Select.Item>
					{/each}
				</Select.Content>
			</Select.Root>
		</div>

		<!-- Year Filter -->
		<div>
			<h3 class="text-md mb-1 font-medium">Year</h3>
			<Select.Root
				selected={{
					value: selectedYear,
					label: yearOptions.find((y) => y.value === selectedYear)?.label || 'All'
				}}
				onSelectedChange={(selected) => handleYearChange(selected?.value)}
			>
				<Select.Trigger class="w-full">
					<Select.Value placeholder="Select year" />
				</Select.Trigger>
				<Select.Content>
					{#each yearOptions as option}
						<Select.Item value={option.value}>
							{option.label}
						</Select.Item>
					{/each}
				</Select.Content>
			</Select.Root>
		</div>

		<!-- Month Filter -->
		<div>
			<h3 class="text-md mb-1 font-medium">Month</h3>
			<Select.Root
				selected={{
					value: selectedMonth,
					label: monthOptions.find((m) => m.value === selectedMonth)?.label || 'All'
				}}
				onSelectedChange={(selected) => handleMonthChange(selected?.value)}
			>
				<Select.Trigger class="w-full">
					<Select.Value placeholder="Select month" />
				</Select.Trigger>
				<Select.Content>
					{#each monthOptions as option}
						<Select.Item value={option.value}>
							{option.label}
						</Select.Item>
					{/each}
				</Select.Content>
			</Select.Root>
		</div>
	</div>

	<!-- Display active filters as badges -->
	<div class="mt-2">
		<div class="flex flex-wrap gap-1">
			{#if selectedPilot !== 'all'}
				<Badge variant="outline" class="flex items-center gap-1 text-xs">
					Pilot: {pilotOptions.find((p) => p.value === selectedPilot)?.label || selectedPilot}
				</Badge>
			{/if}
			{#if selectedYear !== 'all'}
				<Badge variant="outline" class="flex items-center gap-1 text-xs">
					Year: {selectedYear}
				</Badge>
			{/if}
			{#if selectedMonth !== 'all'}
				<Badge variant="outline" class="flex items-center gap-1 text-xs">
					Month: {selectedMonth}
				</Badge>
			{/if}
		</div>
	</div>
</div>
