<script lang="ts">
	import {
		createTest,
		updateTest,
		fetchPx4Versions,
		fetchGliderCompanionVersions,
		fetchQGroundControlVersions,
		fetchReferenceParametersVersions,
		fetchForceSensorVersions,
		fetchLandingStationMercuryVersions,
		fetchRAutopilotFTSVersions,
		fetchFTSCommsServerVersions,
		fetchFTSTriggerAndroidAppVersions
	} from '$lib/api';
	import { userProfile, selectedTestProcedure } from '$lib/stores';
	import { Separator } from '$lib/components/ui/separator/index.js';
	import * as Form from '$lib/components/ui/form';
	import * as Select from '$lib/components/ui/select/index.js';
	import { Input } from '$lib/components/ui/input';
	import { Switch } from '$lib/components/ui/switch/index.js';
	import { Textarea } from '$lib/components/ui/textarea/index.js';
	import { gliders } from '$lib/stores';
	import { Checkbox } from '../checkbox';
	import type { SoftwareVersion } from '$lib/types';
	import { onMount } from 'svelte';

	import { createAnimationTriggerAction } from 'svelte-trigger-action';

	const { triggerAnimation, animationAction } = createAnimationTriggerAction();

	import { type SuperValidated, type Infer, superForm } from 'sveltekit-superforms';
	import { zodClient } from 'sveltekit-superforms/adapters';

	import { z } from 'zod';
	import Card from '../card/card.svelte';

	const formSchema = z
		.object({
			glider: z.number().optional(),
			date: z
				.date()
				.max(new Date(new Date().setHours(23, 59, 59, 0)), "Date can't be in the future"),
			flight_review_log_url: z
				.string()
				.url()
				.includes('https://flight-review.uphi', { message: 'Should be a Flight Review URL' }),
			success_status: z.boolean(),
			simulation: z.boolean(),
			notes: z.string().optional(),
			autopilot_software_version: z.string().optional(),
			jetson_software_version: z.string().optional(),
			qgroundcontrol_version: z.string().optional(),
			reference_parameters_version: z.string().optional(),
			force_sensor_version: z.string().optional(),
			landing_station_mercury_version: z.string().optional(),
			r_autopilot_fts_version: z.string().optional(),
			fts_comms_server_version: z.string().optional(),
			fts_trigger_android_app_version: z.string().optional(),
			manually_cleared: z.boolean().optional()
		})
		.refine((data) => data.simulation || data.glider != -1, {
			message: 'Please choose a glider',
			path: ['glider']
		});

	type FormSchema = typeof formSchema;

	export let data: SuperValidated<Infer<FormSchema>> = {
		glider: -1,
		date: new Date(),
		flight_review_log_url: '',
		success_status: false,
		notes: '',
		simulation: $selectedTestProcedure?.simulation || false,
		autopilot_software_version: undefined,
		jetson_software_version: undefined,
		qgroundcontrol_version: undefined,
		reference_parameters_version: undefined,
		force_sensor_version: undefined,
		landing_station_mercury_version: undefined,
		r_autopilot_fts_version: undefined,
		fts_comms_server_version: undefined,
		fts_trigger_android_app_version: undefined,
		manually_cleared: false
	};

	const form = superForm(data, {
		validators: zodClient(formSchema),
		validationMethod: 'onblur'
	});

	const { form: formData, enhance } = form;

	let submitting_form = false;

	export let update_test_id: number | null = null;
	export let edit_form_user_email: string | null = null;
	let px4Versions: SoftwareVersion[] = [];
	let gliderCompanionVersions: SoftwareVersion[] = [];
	let qgroundcontrolVersions: SoftwareVersion[] = [];
	let referenceParametersVersions: SoftwareVersion[] = [];
	let forceSensorVersions: SoftwareVersion[] = [];
	let landingStationMercuryVersions: SoftwareVersion[] = [];
	let rAutopilotFTSVersions: SoftwareVersion[] = [];
	let ftsCommsServerVersions: SoftwareVersion[] = [];
	let ftsTriggerAndroidAppVersions: SoftwareVersion[] = [];

	function sortVersions(versions: SoftwareVersion[]): SoftwareVersion[] {
		return [...versions].sort((a, b) => {
			const aParts = a.name.split('.').map((p) => parseInt(p) || 0);
			const bParts = b.name.split('.').map((p) => parseInt(p) || 0);

			for (let i = 0; i < Math.max(aParts.length, bParts.length); i++) {
				const aVal = aParts[i] || 0;
				const bVal = bParts[i] || 0;
				if (aVal !== bVal) {
					return bVal - aVal;
				}
			}
			return 0;
		});
	}

	onMount(async () => {
		try {
			const [
				rawPx4Versions,
				rawGCVersions,
				rawQGCVersions,
				rawRefParamsVersions,
				rawForceSensorVersions,
				rawLandingMercuryVersions,
				rawRAutopilotFTSVersions,
				rawFTSCommsVersions,
				rawFTSAndroidVersions
			] = await Promise.all([
				fetchPx4Versions(),
				fetchGliderCompanionVersions(),
				fetchQGroundControlVersions(),
				fetchReferenceParametersVersions(),
				fetchForceSensorVersions(),
				fetchLandingStationMercuryVersions(),
				fetchRAutopilotFTSVersions(),
				fetchFTSCommsServerVersions(),
				fetchFTSTriggerAndroidAppVersions()
			]);
			px4Versions = sortVersions(rawPx4Versions);
			gliderCompanionVersions = sortVersions(rawGCVersions);
			qgroundcontrolVersions = sortVersions(rawQGCVersions);
			referenceParametersVersions = sortVersions(rawRefParamsVersions);
			forceSensorVersions = sortVersions(rawForceSensorVersions);
			landingStationMercuryVersions = sortVersions(rawLandingMercuryVersions);
			rAutopilotFTSVersions = sortVersions(rawRAutopilotFTSVersions);
			ftsCommsServerVersions = sortVersions(rawFTSCommsVersions);
			ftsTriggerAndroidAppVersions = sortVersions(rawFTSAndroidVersions);
			if (px4Versions.length > 0) {
				$formData.autopilot_software_version = px4Versions[0].name;
			}
			if (gliderCompanionVersions.length > 0) {
				$formData.jetson_software_version = gliderCompanionVersions[0].name;
			}
			if (qgroundcontrolVersions.length > 0) {
				$formData.qgroundcontrol_version = qgroundcontrolVersions[0].name;
			}
			if (referenceParametersVersions.length > 0) {
				$formData.reference_parameters_version = referenceParametersVersions[0].name;
			}
			if (forceSensorVersions.length > 0) {
				$formData.force_sensor_version = forceSensorVersions[0].name;
			}
			if (landingStationMercuryVersions.length > 0) {
				$formData.landing_station_mercury_version = landingStationMercuryVersions[0].name;
			}

			if (rAutopilotFTSVersions.length > 0) {
				$formData.r_autopilot_fts_version = rAutopilotFTSVersions[0].name;
			}
			if (ftsCommsServerVersions.length > 0) {
				$formData.fts_comms_server_version = ftsCommsServerVersions[0].name;
			}
			if (ftsTriggerAndroidAppVersions.length > 0) {
				$formData.fts_trigger_android_app_version = ftsTriggerAndroidAppVersions[0].name;
			}
		} catch (error) {
			console.error('Failed to fetch software versions:', error);
		}
	});

	async function submit_form(e: Event) {
		e.preventDefault();
		const result = await form.validateForm({ update: true, focusOnError: true });
		if (!result.valid) {
			triggerAnimation('shake');
			console.log(result.errors);
			return;
		}

		submitting_form = true;

		const glider_id =
			$formData.glider == -1 || !$formData.glider || $formData.simulation
				? null
				: `${$formData.glider}`;

		if (update_test_id) {
			await updateTest(update_test_id, {
				date: $formData.date,
				simulation: $formData.simulation,
				glider: glider_id,
				success_status: $formData.success_status,
				notes: $formData.notes ?? '',
				flight_review_log_url: $formData.flight_review_log_url,
				autopilot_software_version: $formData.autopilot_software_version,
				jetson_software_version: $formData.jetson_software_version,
				qgroundcontrol_version: $formData.qgroundcontrol_version,
				reference_parameters_version: $formData.reference_parameters_version,
				force_sensor_version: $formData.force_sensor_version,
				landing_station_mercury_version: $formData.landing_station_mercury_version,
				r_autopilot_fts_version: $formData.r_autopilot_fts_version,
				fts_comms_server_version: $formData.fts_comms_server_version,
				fts_trigger_android_app_version: $formData.fts_trigger_android_app_version,
				manually_cleared: $formData.manually_cleared
			});
		} else {
			await createTest({
				test_procedure_id: $selectedTestProcedure?.id,
				date: $formData.date,
				simulation: $formData.simulation,
				glider: glider_id,
				success_status: $formData.success_status,
				notes: $formData.notes ?? '',
				flight_review_log_url: $formData.flight_review_log_url,
				automated_execution_status: null,
				tester_id: $userProfile?.id,
				automated: false,
				autopilot_software_version: $formData.autopilot_software_version,
				jetson_software_version: $formData.jetson_software_version,
				qgroundcontrol_version: $formData.qgroundcontrol_version,
				reference_parameters_version: $formData.reference_parameters_version,
				force_sensor_version: $formData.force_sensor_version,
				landing_station_mercury_version: $formData.landing_station_mercury_version,
				r_autopilot_fts_version: $formData.r_autopilot_fts_version,
				fts_comms_server_version: $formData.fts_comms_server_version,
				fts_trigger_android_app_version: $formData.fts_trigger_android_app_version,
				manually_cleared: $formData.manually_cleared
			});
		}

		submitting_form = false;

		dialog_open = false;
	}

	export let dialog_open;
	$: isSimulation = $formData.simulation;
	$: isFlightTest = !isSimulation;
	$: showAllSoftwareVersions = isFlightTest;
</script>

<form method="POST" use:enhance on:submit={submit_form}>
	<Card>
		<div class="flex flex-col p-5">
			<div class="flex items-center justify-between">
				<p>Procedure</p>
				<div class="mx-1 flex-grow">
					<Separator />
				</div>
				<p>{$selectedTestProcedure?.name}</p>
			</div>
			<div class="mt-5 flex items-center justify-between">
				<p>Version</p>
				<div class="mx-1 flex-grow">
					<Separator />
				</div>
				<p>v{$selectedTestProcedure?.version}</p>
			</div>
			<div class="mb-7 mt-5 flex items-center justify-between">
				<p>Tester</p>
				<div class="mx-1 flex-grow">
					<Separator />
				</div>
				<p>{edit_form_user_email ?? $userProfile?.email}</p>
			</div>
			{#if $selectedTestProcedure?.simulation}
				<Form.Field {form} name="simulation">
					<Form.Control let:attrs>
						<div class="flex flex-row items-center">
							<Checkbox
								{...attrs}
								bind:checked={$formData.simulation}
								disabled={!$selectedTestProcedure?.hil_test && !$selectedTestProcedure?.field_test}
							/>
							<div class="ml-2 space-y-1 leading-none">
								<Form.Label>This test was done in the Simulator</Form.Label>
							</div>
							<input name={attrs.name} value={$formData.simulation} hidden />
						</div>
					</Form.Control>

					<Form.FieldErrors />
				</Form.Field>
			{/if}
			{#if !$formData.simulation}
				<Form.Field {form} name="glider">
					<Form.Control let:attrs>
						<Form.Label>Glider</Form.Label>
						<Select.Root
							selected={{
								value: $formData.glider,
								label: $gliders.find((x) => x.id == $formData.glider)?.name
							}}
							onSelectedChange={(v) => {
								if (v) $formData.glider = v.value;
							}}
						>
							<Select.Trigger {...attrs}>
								<Select.Value placeholder="Select a glider" />
							</Select.Trigger>
							<Select.Content>
								{#each $gliders.filter((x) => x.inUse) as option}
									<Select.Item value={option.id} label={option.name} />
								{/each}
							</Select.Content>
						</Select.Root>
					</Form.Control>
					<Form.FieldErrors />
				</Form.Field>
			{/if}

			<Form.Field {form} name="autopilot_software_version">
				<Form.Control let:attrs>
					<Form.Label>Autopilot Version</Form.Label>
					<Select.Root
						selected={{
							value: $formData.autopilot_software_version,
							label: $formData.autopilot_software_version
						}}
						onSelectedChange={(v) => {
							if (v) $formData.autopilot_software_version = v.value;
						}}
					>
						<Select.Trigger {...attrs}>
							<Select.Value placeholder="Select PX4 version" />
						</Select.Trigger>
						<Select.Content>
							{#each px4Versions as option}
								<Select.Item value={option.name} label={option.name} />
							{/each}
						</Select.Content>
					</Select.Root>
				</Form.Control>
				<Form.FieldErrors />
			</Form.Field>

			<Form.Field {form} name="jetson_software_version">
				<Form.Control let:attrs>
					<Form.Label>Jetson Version</Form.Label>
					<Select.Root
						selected={{
							value: $formData.jetson_software_version,
							label: $formData.jetson_software_version
						}}
						onSelectedChange={(v) => {
							if (v) $formData.jetson_software_version = v.value;
						}}
					>
						<Select.Trigger {...attrs}>
							<Select.Value placeholder="Select Glider-Companion version" />
						</Select.Trigger>
						<Select.Content>
							{#each gliderCompanionVersions as option}
								<Select.Item value={option.name} label={option.name} />
							{/each}
						</Select.Content>
					</Select.Root>
				</Form.Control>
				<Form.FieldErrors />
			</Form.Field>

			<Form.Field {form} name="qgroundcontrol_version">
				<Form.Control let:attrs>
					<Form.Label>QGC Version</Form.Label>
					<Select.Root
						selected={{
							value: $formData.qgroundcontrol_version,
							label: $formData.qgroundcontrol_version
						}}
						onSelectedChange={(v) => {
							if (v) $formData.qgroundcontrol_version = v.value;
						}}
					>
						<Select.Trigger {...attrs}>
							<Select.Value placeholder="Select QGroundControl version" />
						</Select.Trigger>
						<Select.Content>
							{#each qgroundcontrolVersions as option}
								<Select.Item value={option.name} label={option.name} />
							{/each}
						</Select.Content>
					</Select.Root>
				</Form.Control>
				<Form.FieldErrors />
			</Form.Field>

			<Form.Field {form} name="reference_parameters_version">
				<Form.Control let:attrs>
					<Form.Label>Reference Parameters Version</Form.Label>
					<Select.Root
						selected={{
							value: $formData.reference_parameters_version,
							label: $formData.reference_parameters_version
						}}
						onSelectedChange={(v) => {
							if (v) $formData.reference_parameters_version = v.value;
						}}
					>
						<Select.Trigger {...attrs}>
							<Select.Value placeholder="Select Reference Parameters version" />
						</Select.Trigger>
						<Select.Content>
							{#each referenceParametersVersions as option}
								<Select.Item value={option.name} label={option.name} />
							{/each}
						</Select.Content>
					</Select.Root>
				</Form.Control>
				<Form.FieldErrors />
			</Form.Field>
			{#if showAllSoftwareVersions}
				<Form.Field {form} name="force_sensor_version">
					<Form.Control let:attrs>
						<Form.Label>Force Sensor Version</Form.Label>
						<Select.Root
							selected={{
								value: $formData.force_sensor_version,
								label: $formData.force_sensor_version
							}}
							onSelectedChange={(v) => {
								if (v) $formData.force_sensor_version = v.value;
							}}
						>
							<Select.Trigger {...attrs}>
								<Select.Value placeholder="Select Force Sensor version" />
							</Select.Trigger>
							<Select.Content>
								{#each forceSensorVersions as option}
									<Select.Item value={option.name} label={option.name} />
								{/each}
							</Select.Content>
						</Select.Root>
					</Form.Control>
					<Form.FieldErrors />
				</Form.Field>
				<Form.Field {form} name="landing_station_mercury_version">
					<Form.Control let:attrs>
						<Form.Label>Landing Station Mercury Version</Form.Label>
						<Select.Root
							selected={{
								value: $formData.landing_station_mercury_version,
								label: $formData.landing_station_mercury_version
							}}
							onSelectedChange={(v) => {
								if (v) $formData.landing_station_mercury_version = v.value;
							}}
						>
							<Select.Trigger {...attrs}>
								<Select.Value placeholder="Select Landing Station Mercury version" />
							</Select.Trigger>
							<Select.Content>
								{#each landingStationMercuryVersions as option}
									<Select.Item value={option.name} label={option.name} />
								{/each}
							</Select.Content>
						</Select.Root>
					</Form.Control>
					<Form.FieldErrors />
				</Form.Field>
			{/if}

			<Form.Field {form} name="date">
				<Form.Control let:attrs>
					<Form.Label>Date</Form.Label>
					<Input
						type="date"
						{...attrs}
						value={$formData.date.toISOString().split('T')[0]}
						on:change={(x) => {
							$formData.date = new Date(x.currentTarget?.value);
						}}
					/>
				</Form.Control>
				<Form.FieldErrors />
			</Form.Field>
			{#if showAllSoftwareVersions}
				<Form.Field {form} name="r_autopilot_fts_version">
					<Form.Control let:attrs>
						<Form.Label>FTS Pixhawk Version</Form.Label>
						<Select.Root
							selected={{
								value: $formData.r_autopilot_fts_version,
								label: $formData.r_autopilot_fts_version
							}}
							onSelectedChange={(v) => {
								if (v) $formData.r_autopilot_fts_version = v.value;
							}}
						>
							<Select.Trigger {...attrs}>
								<Select.Value placeholder="Select R-Autopilot (FTS) version" />
							</Select.Trigger>
							<Select.Content>
								{#each rAutopilotFTSVersions as option}
									<Select.Item value={option.name} label={option.name} />
								{/each}
							</Select.Content>
						</Select.Root>
					</Form.Control>
					<Form.FieldErrors />
				</Form.Field>
			{/if}

			<Form.Field {form} name="flight_review_log_url">
				<Form.Control let:attrs>
					<Form.Label>Flight Review Log URL</Form.Label>
					<Input type="text" {...attrs} bind:value={$formData.flight_review_log_url} />
				</Form.Control>
				<Form.FieldErrors />
			</Form.Field>
			{#if showAllSoftwareVersions}
				<Form.Field {form} name="fts_comms_server_version">
					<Form.Control let:attrs>
						<Form.Label>FTS Raspberry Pi Version</Form.Label>
						<Select.Root
							selected={{
								value: $formData.fts_comms_server_version,
								label: $formData.fts_comms_server_version
							}}
							onSelectedChange={(v) => {
								if (v) $formData.fts_comms_server_version = v.value;
							}}
						>
							<Select.Trigger {...attrs}>
								<Select.Value placeholder="Select FTS Comms Server version" />
							</Select.Trigger>
							<Select.Content>
								{#each ftsCommsServerVersions as option}
									<Select.Item value={option.name} label={option.name} />
								{/each}
							</Select.Content>
						</Select.Root>
					</Form.Control>
					<Form.FieldErrors />
				</Form.Field>
				<Form.Field {form} name="fts_trigger_android_app_version">
					<Form.Control let:attrs>
						<Form.Label>FTS App Version</Form.Label>
						<Select.Root
							selected={{
								value: $formData.fts_trigger_android_app_version,
								label: $formData.fts_trigger_android_app_version
							}}
							onSelectedChange={(v) => {
								if (v) $formData.fts_trigger_android_app_version = v.value;
							}}
						>
							<Select.Trigger {...attrs}>
								<Select.Value placeholder="Select FTS Trigger Android App version" />
							</Select.Trigger>
							<Select.Content>
								{#each ftsTriggerAndroidAppVersions as option}
									<Select.Item value={option.name} label={option.name} />
								{/each}
							</Select.Content>
						</Select.Root>
					</Form.Control>
					<Form.FieldErrors />
				</Form.Field>
			{/if}

			<Form.Field
				{form}
				name="success_status"
				class="flex flex-row items-center justify-between rounded-lg border p-4"
			>
				<Form.Control let:attrs>
					<Switch {...attrs} includeInput bind:checked={$formData.success_status} />
					<div class="ml-10">
						<Form.Label>Test Successful</Form.Label>
						<Form.Description>
							I acknowledge that this test was successful according to the setup and expectations of
							the test procedure.
						</Form.Description>
					</div>
				</Form.Control>
			</Form.Field>

			<Form.Field
				{form}
				name="manually_cleared"
				class="flex flex-row items-center justify-between rounded-lg border p-4"
			>
				<Form.Control let:attrs>
					<Switch {...attrs} includeInput bind:checked={$formData.manually_cleared} />
					<div class="ml-10">
						<Form.Label>Manually Cleared</Form.Label>
						<Form.Description>
							Mark this test as manually cleared. It will not influence success state calculations
							and will not appear in exported data.
						</Form.Description>
					</div>
				</Form.Control>
			</Form.Field>

			<Form.Field {form} name="notes">
				<Form.Control let:attrs>
					<Form.Label>Extra Notes</Form.Label>
					<Textarea
						{...attrs}
						placeholder="Anything to add?"
						class="resize-none"
						bind:value={$formData.notes}
					/>
				</Form.Control>
				<Form.FieldErrors />
			</Form.Field>
		</div>
	</Card>
	<div class="mt-3 flex justify-end" use:animationAction>
		<Form.Button>
			{#if submitting_form}
				<span class="mr-2 h-2 w-2 animate-spin rounded-full border-b-2 border-t-2 border-secondary"
				></span>
			{/if}
			{#if update_test_id}Update Report
			{:else}
				Submit Report
			{/if}
		</Form.Button>
	</div>
</form>
