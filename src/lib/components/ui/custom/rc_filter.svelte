<script lang="ts">
	import { Checkbox } from '$lib/components/ui/checkbox';
	import { createEventDispatcher } from 'svelte';
	import type { SoftwareVersion } from '$lib/types';
	import { getAvailableRCTypes } from '$lib/utils';

	export let versions: SoftwareVersion[] = [];
	export let selectedVersion: string = '';
	export let selectedRCs: string[] = [];

	const dispatch = createEventDispatcher();
	$: availableRCTypes = getAvailableRCTypes(versions, selectedVersion);
	function formatRCType(rcType: string): string {
		if (rcType === 'official') return 'Official';
		return rcType.toUpperCase(); // rc1 -> RC1
	}

	function handleRCChange(rcType: string, checked: boolean) {
		let newSelectedRCs: string[];

		if (checked) {
			newSelectedRCs = [...selectedRCs, rcType];
		} else {
			newSelectedRCs = selectedRCs.filter((r) => r !== rcType);
		}

		dispatch('change', {
			selectedRCs: newSelectedRCs
		});
	}

	function selectAllRCs() {
		dispatch('change', {
			selectedRCs: [...availableRCTypes]
		});
	}

	function deselectAllRCs() {
		dispatch('change', {
			selectedRCs: []
		});
	}

	function selectOnlyOfficial() {
		dispatch('change', {
			selectedRCs: availableRCTypes.includes('official') ? ['official'] : []
		});
	}
	$: allSelected = availableRCTypes.length > 0 && selectedRCs.length === availableRCTypes.length;
	$: noneSelected = selectedRCs.length === 0;
	$: onlyOfficialSelected = selectedRCs.length === 1 && selectedRCs.includes('official');
	$: if (
		availableRCTypes.length > 0 &&
		selectedRCs.length === 0 &&
		selectedVersion &&
		selectedVersion !== 'all' &&
		selectedVersion !== 'no_version'
	) {
		setTimeout(() => {
			if (availableRCTypes.includes('official')) {
				dispatch('change', {
					selectedRCs: ['official']
				});
			} else {
				dispatch('change', {
					selectedRCs: [...availableRCTypes]
				});
			}
		}, 0);
	}
</script>

{#if availableRCTypes.length > 0}
	<div class="mt-2 space-y-2">
		<div class="flex items-center justify-between">
			<h4 class="text-sm font-medium text-muted-foreground">
				Release Candidates for {selectedVersion}
			</h4>
			<div class="flex gap-1 text-xs">
				<button
					class="text-blue-600 hover:text-blue-800 disabled:text-gray-400"
					on:click={selectOnlyOfficial}
					disabled={onlyOfficialSelected || !availableRCTypes.includes('official')}
				>
					Official
				</button>
				<span class="text-muted-foreground">|</span>
				<button
					class="text-blue-600 hover:text-blue-800 disabled:text-gray-400"
					on:click={selectAllRCs}
					disabled={allSelected}
				>
					All
				</button>
				<span class="text-muted-foreground">|</span>
				<button
					class="text-blue-600 hover:text-blue-800 disabled:text-gray-400"
					on:click={deselectAllRCs}
					disabled={noneSelected}
				>
					None
				</button>
			</div>
		</div>

		<div class="flex flex-wrap gap-2">
			{#each availableRCTypes as rcType}
				<div class="flex items-center space-x-1">
					<Checkbox
						id="rc-{rcType}"
						checked={selectedRCs.includes(rcType)}
						onCheckedChange={(checked) => handleRCChange(rcType, checked === true)}
					/>
					<label
						for="rc-{rcType}"
						class="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
						class:text-green-700={rcType === 'official'}
						class:font-semibold={rcType === 'official'}
					>
						{formatRCType(rcType)}
					</label>
				</div>
			{/each}
		</div>

		{#if selectedRCs.length > 0}
			<div class="text-xs text-muted-foreground">
				Selected: {selectedRCs.map((rc) => formatRCType(rc)).join(', ')}
			</div>
		{/if}
	</div>
{/if}
