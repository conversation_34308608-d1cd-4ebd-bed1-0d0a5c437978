<script lang="ts">
	import type { TestProcedure } from '$lib/types';
	import { Badge } from '$lib/components/ui/badge';
	import { cn } from '$lib/utils';
	import { selectedTestProcedure, testDates } from '$lib/stores';
	import { SquareFunction } from 'lucide-svelte';
	import * as Tooltip from '$lib/components/ui/tooltip';
	export const selected = true;
	import { get_procedure_last_test_date } from '$lib/utils';
	export let data: TestProcedure;
</script>

<div
	class={cn(
		$selectedTestProcedure?.id == data.id
			? 'bg-stone-200 shadow-sm'
			: 'bg-primary-foreground hover:bg-secondary',
		'my-2 rounded-lg p-2 hover:cursor-pointer'
	)}
>
	<p class={data.obsolete ? 'italic text-muted-foreground' : ''}>
		{data.external_id} - {data.name} (v{data.version})
	</p>
	<div class="flex justify-between">
		<div class="flex justify-start">
			<p class="text-sm text-muted-foreground">
				{data.group.name} - {data.test_type}
				{#if $testDates}- {#key $testDates}
						<Tooltip.Root openDelay={100}>
							<Tooltip.Trigger>
								{get_procedure_last_test_date(data)}
							</Tooltip.Trigger>
							<Tooltip.Content>
								<p>This procedure was last tested on this date</p>
							</Tooltip.Content>
						</Tooltip.Root>
					{/key}{/if}
			</p>
		</div>
		<div class="flex justify-end">
			{#if data.automated}
				<Tooltip.Root openDelay={200}>
					<Tooltip.Trigger>
						<SquareFunction class={cn('mr-1 h-5 w-5 text-foreground')} />
					</Tooltip.Trigger>
					<Tooltip.Content>
						<p>Automated Procedure</p>
					</Tooltip.Content>
				</Tooltip.Root>
			{/if}
			{#if data.simulation}
				<Badge variant={data.obsolete ? 'outline' : 'default'} class="mr-1">SIM</Badge>
			{/if}
			{#if data.field_test}
				<Badge variant={data.obsolete ? 'outline' : 'default'} class="mr-1">FT</Badge>
			{/if}
			{#if data.hil_test}
				<Badge variant={data.obsolete ? 'outline' : 'default'} class="mr-1">HIL</Badge>
			{/if}
			{#if data.tags}
				{#each data.tags as tag}
					<Badge class="mr-0.5">{tag.display_name}</Badge>
				{/each}
			{/if}
		</div>
	</div>
</div>
