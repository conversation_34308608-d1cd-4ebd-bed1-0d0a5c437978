<script lang="ts">
	import { createGroup, createTestProcedure, updateTestProcedure, fetchTags } from '$lib/api';
	import { Button } from '$lib/components/ui/button/index.js';
	import {
		VTOLModes,
		windModes,
		simulationWorlds,
		autopilotModes,
		testProcedureGroups,
		selectedTestProcedure
	} from '$lib/stores';
	import * as Form from '$lib/components/ui/form';
	import * as Select from '$lib/components/ui/select/index.js';
	import { Input } from '$lib/components/ui/input';
	import { Textarea } from '$lib/components/ui/textarea/index.js';
	import { Checkbox } from '$lib/components/ui/checkbox/index.js';
	import * as Tabs from '$lib/components/ui/tabs';
	import { Progress } from '$lib/components/ui/progress';
	import * as Popover from '$lib/components/ui/popover/index.js';
	import * as Command from '$lib/components/ui/command/index.js';
	import { cn } from '$lib/utils.js';
	import { buttonVariants } from '$lib/components/ui/button/index.js';
	import { onMount, tick } from 'svelte';
	import CaretSort from 'svelte-radix/CaretSort.svelte';
	import Check from 'svelte-radix/Check.svelte';
	import * as Dialog from '$lib/components/ui/dialog';
	import type { Tag } from '$lib/types';
	import { Info } from 'lucide-svelte';

	import { createAnimationTriggerAction } from 'svelte-trigger-action';

	const { triggerAnimation, animationAction } = createAnimationTriggerAction();

	let loadingTags = true;
	// import { formSchema, type FormSchema } from "./form_schema";
	import { type SuperValidated, type Infer, superForm } from 'sveltekit-superforms';
	import { zodClient } from 'sveltekit-superforms/adapters';

	import { z } from 'zod';
	import Card from '../card/card.svelte';

	const test_type_options = ['Default', 'Normal', 'Emergency', 'Contingency'] as const;
	const priority_level_options = ['High', 'Medium', 'Low'] as const;

	const parse_array = (str: string) =>
		str
			.split('\n')
			.map((x) => x.trim())
			.filter((x) => x.length > 0);
	const validate_array = (str: string) => parse_array(str).length > 0;

	let selectedTagIds: number[] = [];
	let tagList: Tag[] = [];
	let selectedTagBools: boolean[] = [];

	onMount(async () => {
		try {
			tagList = await fetchTags();
			loadingTags = false;

			selectedTagBools = tagList.map(
				(tag) =>
					tag.is_default || (edit && selectedTestProcedure?.tags?.some((t) => t.id === tag.id))
			);

			if (edit && selectedTestProcedure?.tags) {
				const existingTagIds = selectedTestProcedure.tags.map((tag) => tag.id);
				selectedTagIds = [...new Set([...selectedTagIds, ...existingTagIds])];
			}
		} catch (error) {
			console.error(error);
			loadingTags = false;
		}
	});

	const formSchema = z
		.object({
			name: z.string().min(1, 'Name is required'),
			test_type: z.string().min(1, 'Test type is required'),
			group: z.number().min(-1, 'Group is required'),
			priority_level: z.string().min(1, 'Priority level is required'),
			simulation: z.boolean(),
			field_test: z.boolean(),
			hil_test: z.boolean(),
			qgroundcontrol: z.boolean(),
			mdp: z.boolean(),
			precland_docker: z.boolean(),
			sim_world: z.string(),
			autopilot_mode: z.string().min(1, 'Autopilot mode is required'),
			flight_mode: z.string().min(1, 'VTOL mode is required'),
			mission_plan: z.string(),
			wind_mode: z.enum([
				'None',
				'Side Wind',
				'Head Wind',
				'Tail Wind',
				'Vertical Wind',
				'Environment',
				'Arbitrary'
			]),
			wind_speed: z.number({ coerce: true }).int().min(0).max(100),
			procedures_simulation: z.string(),
			procedures_field_test: z.string(),
			procedures_hil_test: z.string(),
			expectations_simulation: z.string(),
			expectations_field_test: z.string(),
			expectations_hil_test: z.string(),
			tag_ids: z.array(z.number()).optional()
		})
		.refine((data) => data.simulation || data.hil_test || data.field_test, {
			message: 'At least one of SIM, HIL or IRL should be checked.',
			path: ['env']
		})
		.refine((data) => (data.simulation && data.sim_world.length) || !data.simulation, {
			message: 'Sim World should be specified for SIM procedures',
			path: ['sim_world']
		})
		.refine((data) => (data.autopilot_mode == 'Mission' ? data.mission_plan.length : true), {
			message: 'Mission Plan should be specified for Mission autopilot mode',
			path: ['mission_plan']
		})
		.refine((data) => (data.simulation ? validate_array(data.procedures_simulation) : true), {
			message: 'Please describe the procedure steps in the SIM',
			path: ['procedures_simulation']
		})
		.refine((data) => (data.simulation ? validate_array(data.expectations_simulation) : true), {
			message: 'Please describe the expected outcomes in the SIM',
			path: ['expectations_simulation']
		})
		.refine((data) => (data.field_test ? validate_array(data.procedures_field_test) : true), {
			message: 'Please describe the procedure steps in the Field Test',
			path: ['procedures_field_test']
		})
		.refine((data) => (data.field_test ? validate_array(data.expectations_field_test) : true), {
			message: 'Please describe the expected outcomes in the Field Test',
			path: ['expectations_field_test']
		})
		.refine((data) => (data.hil_test ? validate_array(data.procedures_hil_test) : true), {
			message: 'Please describe the procedure steps in the HIL Test',
			path: ['procedures_hil_test']
		})
		.refine((data) => (data.hil_test ? validate_array(data.expectations_hil_test) : true), {
			message: 'Please describe the expected outcomes in the HIL Test',
			path: ['expectations_hil_test']
		});

	type FormSchema = typeof formSchema;

	export let edit = false;

	let data: SuperValidated<Infer<FormSchema>> = {
		name: edit ? $selectedTestProcedure?.name : '',
		version: edit ? $selectedTestProcedure?.version : 1,
		test_type: edit ? $selectedTestProcedure?.test_type : '',
		group: edit ? $selectedTestProcedure?.group.id : null,
		priority_level: edit ? $selectedTestProcedure?.priority_level : '',
		simulation: edit ? $selectedTestProcedure?.simulation : false,
		field_test: edit ? $selectedTestProcedure?.field_test : false,
		hil_test: edit ? $selectedTestProcedure?.hil_test : false,
		qgroundcontrol: edit ? $selectedTestProcedure?.qgroundcontrol : false,
		mdp: edit ? $selectedTestProcedure?.mdp : false,
		precland_docker: edit ? $selectedTestProcedure?.precland_docker : false,
		sim_world: edit ? $selectedTestProcedure?.sim_world : '',
		autopilot_mode: edit ? $selectedTestProcedure?.autopilot_mode : '',
		flight_mode: edit ? $selectedTestProcedure?.vtol_mode : '',
		mission_plan: edit ? ($selectedTestProcedure?.mission_plan ?? '') : '',
		wind_mode: edit ? $selectedTestProcedure?.wind_mode : 'None',
		wind_speed: edit ? $selectedTestProcedure?.wind_speed : 0,
		procedures_simulation: edit ? $selectedTestProcedure?.procedures_simulation.join('\n') : '',
		procedures_field_test: edit ? $selectedTestProcedure?.procedures_field.join('\n') : '',
		procedures_hil_test: edit ? $selectedTestProcedure?.procedures_hil.join('\n') : '',
		expectations_simulation: edit ? $selectedTestProcedure?.expectations_simulation.join('\n') : '',
		expectations_field_test: edit ? $selectedTestProcedure?.expectations_field.join('\n') : '',
		expectations_hil_test: edit ? $selectedTestProcedure?.expectations_hil.join('\n') : '',
		tag_ids:
			edit && selectedTestProcedure?.tags
				? selectedTestProcedure.tags.map((tag) => Number(tag.id))
				: []
	};

	const form = superForm(data, {
		validators: zodClient(formSchema),
		validationMethod: 'onblur',
		dataType: 'json'
	});

	const { form: formData, enhance } = form;
	let update_procedure_version = false;

	function syncSelectedTags() {
		selectedTagIds = tagList
			.map((tag, index) => (selectedTagBools[index] ? tag.id : null))
			.filter((id) => id !== null);
	}

	async function submit_form(e: Event) {
		e.preventDefault();
		syncSelectedTags();

		$formData.tag_ids = selectedTagIds;
		const result = await form.validateForm({ update: true, focusOnError: true });
		if (!result.valid) {
			triggerAnimation('shake');
			console.log(result.errors);
			return;
		}
		submitting_form = true;

		let group_id = $formData.group;
		// In case we need to create a new group
		if (group_id == -1) {
			const group_creation_response = await createGroup({ name: group_search_term });
			group_id = group_creation_response.id;
		}

		$formData.tag_ids = [...selectedTagIds];

		if (edit)
			await updateTestProcedure($selectedTestProcedure.id, {
				name: $formData.name.toUpperCase(),
				test_type: $formData.test_type.toUpperCase(),
				group_id: group_id,
				priority_level: $formData.priority_level.toLowerCase(),
				simulation: $formData.simulation,
				field_test: $formData.field_test,
				hil_test: $formData.hil_test,
				qgroundcontrol: $formData.qgroundcontrol,
				mdp: $formData.mdp,
				precland_docker: $formData.precland_docker,
				sim_world: $formData.sim_world.length > 0 ? $formData.sim_world : null,
				autopilot_mode: $formData.autopilot_mode,
				vtol_mode: $formData.flight_mode,
				mission_plan: $formData.mission_plan,
				wind_mode: $formData.wind_mode,
				wind_speed: $formData.wind_speed,
				procedures_simulation: $formData.simulation
					? parse_array($formData.procedures_simulation)
					: [],
				procedures_field: $formData.field_test ? parse_array($formData.procedures_field_test) : [],
				procedures_hil: $formData.hil_test ? parse_array($formData.procedures_hil_test) : [],
				expectations_simulation: $formData.simulation
					? parse_array($formData.expectations_simulation)
					: [],
				expectations_field: $formData.field_test
					? parse_array($formData.expectations_field_test)
					: [],
				expectations_hil: $formData.hil_test ? parse_array($formData.expectations_hil_test) : [],
				automated: false,
				version: update_procedure_version
					? ($selectedTestProcedure?.version ?? 0) + 1
					: $selectedTestProcedure?.version,
				external_id: $selectedTestProcedure.external_id,
				tag_ids: $formData.tag_ids
			});
		else
			await createTestProcedure({
				name: $formData.name.toUpperCase(),
				test_type: $formData.test_type.toUpperCase(),
				group_id: group_id,
				priority_level: $formData.priority_level.toLowerCase(),
				simulation: $formData.simulation,
				field_test: $formData.field_test,
				hil_test: $formData.hil_test,
				qgroundcontrol: $formData.qgroundcontrol,
				mdp: $formData.mdp,
				precland_docker: $formData.precland_docker,
				sim_world: $formData.sim_world.length > 0 ? $formData.sim_world : null,
				autopilot_mode: $formData.autopilot_mode,
				vtol_mode: $formData.flight_mode,
				mission_plan: $formData.mission_plan,
				wind_mode: $formData.wind_mode,
				wind_speed: $formData.wind_speed,
				procedures_simulation: parse_array($formData.procedures_simulation),
				procedures_field: parse_array($formData.procedures_field_test),
				procedures_hil: parse_array($formData.procedures_hil_test),
				expectations_simulation: parse_array($formData.expectations_simulation),
				expectations_field: parse_array($formData.expectations_field_test),
				expectations_hil: parse_array($formData.expectations_hil_test),
				automated: false,
				version: 1,
				tag_ids: $formData.tag_ids
			});
		submitting_form = false;
		location.reload();
	}

	let form_page = 'first';

	let open = false;

	let new_version_popup_open = false;

	// We want to refocus the trigger button when the user selects
	// an item from the list so users can continue navigating the
	// rest of the form with the keyboard.
	function closeAndFocusTrigger(triggerId: string) {
		open = false;
		tick().then(() => {
			try {
				const element = document.getElementById(triggerId);
				if (element && element.focus) {
					element.focus();
				}
			} catch (error) {
				console.warn('Could not focus element:', triggerId, error);
			}
		});
	}

	$: group_search_term = '';

	let submitting_form = false;
</script>

<!-- <Combobox values={$testProcedures.map(p => p.name)}/> -->
<form method="POST" use:enhance on:submit={submit_form}>
	<Progress value={form_page === 'first' ? 50 : 100} class="my-2" />
	<Card class="h-full w-full">
		<Tabs.Root bind:value={form_page}>
			<Tabs.Content value="first">
				<div class="flex">
					<div class="flex flex-grow flex-col p-5">
						<Form.Field {form} name="name">
							<Form.Control let:attrs>
								<Form.Label>Name</Form.Label>
								<Input type="text" {...attrs} bind:value={$formData.name} />
							</Form.Control>
							<Form.FieldErrors />
						</Form.Field>

						<Form.Field {form} name="test_type">
							<Form.Control let:attrs>
								<Form.Label>Test Type</Form.Label>
								<Select.Root
									selected={{ value: $formData.test_type, label: $formData.test_type }}
									onSelectedChange={(v) => {
										v && ($formData.test_type = v.value);
									}}
								>
									<Select.Trigger {...attrs}>
										<Select.Value placeholder="Select a test type" />
									</Select.Trigger>
									<Select.Content>
										{#each test_type_options as option}
											<Select.Item value={option} label={option} />
										{/each}
									</Select.Content>
								</Select.Root>
							</Form.Control>
							<Form.FieldErrors />
						</Form.Field>
						<Form.Field {form} name="group" class="flex flex-col">
							<Popover.Root bind:open let:ids>
								<Form.Control let:attrs>
									<Form.Label>Group</Form.Label>
									<Popover.Trigger
										class={cn(
											buttonVariants({ variant: 'outline' }),
											'w-full justify-between',
											!$formData.group && 'text-muted-foreground'
										)}
										role="combobox"
										{...attrs}
									>
										{$formData.group == -1
											? `Create a new group: ${group_search_term}`
											: ($testProcedureGroups.find((f) => f.id === $formData.group)?.name ??
												'Select group')}
										<CaretSort class="ml-2 h-4 w-4 shrink-0 opacity-50" />
									</Popover.Trigger>
									<input hidden value={$formData.group} name={attrs.name} />
								</Form.Control>
								<Popover.Content class="w-1/2 p-0">
									<Command.Root>
										<Command.Input
											placeholder="Search a group..."
											class="h-9"
											bind:value={group_search_term}
										/>
										<Command.List>
											{#if group_search_term.length > 0}
												<Command.Item
													onSelect={() => {
														$formData.group = -1;
														closeAndFocusTrigger(ids.trigger);
													}}
													alwaysRender={true}
													value="Create a new Group"
													><span>Create a new group "{group_search_term}"</span></Command.Item
												>
											{/if}
											{#each $testProcedureGroups as group}
												<Command.Item
													value={group.name}
													onSelect={() => {
														$formData.group = group.id;
														closeAndFocusTrigger(ids.trigger);
													}}
												>
													{group.name}
													<Check
														class={cn(
															'ml-auto h-4 w-4',
															group.id !== $formData.group && 'text-transparent'
														)}
													/>
												</Command.Item>
											{/each}
										</Command.List>
									</Command.Root>
								</Popover.Content>
							</Popover.Root>
							<Form.FieldErrors />
						</Form.Field>

						<Form.Field {form} name="priority_level">
							<Form.Control let:attrs>
								<Form.Label>Priority Level</Form.Label>
								<Select.Root
									selected={{ value: $formData.priority_level, label: $formData.priority_level }}
									onSelectedChange={(v) => {
										v && ($formData.priority_level = v.value);
									}}
								>
									<Select.Trigger {...attrs}>
										<Select.Value placeholder="Select a priority level" />
									</Select.Trigger>
									<Select.Content>
										{#each priority_level_options as option}
											<Select.Item value={option} label={option} />
										{/each}
									</Select.Content>
								</Select.Root>
							</Form.Control>
							<Form.FieldErrors />
						</Form.Field>

						<div class="mt-1 grid grid-cols-3 grid-rows-3">
							<Form.Field {form} name="simulation">
								<Form.Control let:attrs>
									<div class="flex flex-row items-start space-x-3 space-y-0 p-1">
										<Checkbox {...attrs} bind:checked={$formData.simulation} />
										<div class="space-y-1 leading-none">
											<Form.Label>Simulation</Form.Label>
										</div>
										<input name={attrs.name} value={$formData.simulation} hidden />
									</div>
								</Form.Control>

								<Form.FieldErrors />
							</Form.Field>

							<Form.Field
								{form}
								name="field_test"
								class="flex flex-row items-start space-x-3 space-y-0 p-1"
							>
								<Form.Control let:attrs>
									<Checkbox {...attrs} bind:checked={$formData.field_test} />
									<div class="space-y-1 leading-none">
										<Form.Label>Field Test</Form.Label>
									</div>
									<input name={attrs.name} value={$formData.field_test} hidden />
								</Form.Control>
							</Form.Field>

							<Form.Field
								{form}
								name="hil_test"
								class="flex flex-row items-start space-x-3 space-y-0 p-1"
							>
								<Form.Control let:attrs>
									<Checkbox {...attrs} bind:checked={$formData.hil_test} />
									<div class="space-y-1 leading-none">
										<Form.Label>HIL Test</Form.Label>
									</div>
									<input name={attrs.name} value={$formData.hil_test} hidden />
								</Form.Control>
							</Form.Field>

							<Form.Field
								{form}
								name="qgroundcontrol"
								class="flex flex-row items-start space-x-3 space-y-0 p-1"
							>
								<Form.Control let:attrs>
									<Checkbox {...attrs} bind:checked={$formData.qgroundcontrol} />
									<div class="space-y-1 leading-none">
										<Form.Label>QGroundControl</Form.Label>
									</div>
									<input name={attrs.name} value={$formData.qgroundcontrol} hidden />
								</Form.Control>
							</Form.Field>

							<Form.Field
								{form}
								name="mdp"
								class="flex flex-row items-start space-x-3 space-y-0 p-1"
							>
								<Form.Control let:attrs>
									<Checkbox {...attrs} bind:checked={$formData.mdp} />
									<div class="space-y-1 leading-none">
										<Form.Label>MDP</Form.Label>
									</div>
									<input name={attrs.name} value={$formData.mdp} hidden />
								</Form.Control>
							</Form.Field>

							<Form.Field
								{form}
								name="precland_docker"
								class="flex flex-row items-start space-x-3 space-y-0 p-1"
							>
								<Form.Control let:attrs>
									<Checkbox {...attrs} bind:checked={$formData.precland_docker} />
									<div class="space-y-1 leading-none">
										<Form.Label>Precland Docker</Form.Label>
									</div>
									<input name={attrs.name} value={$formData.precland_docker} hidden />
								</Form.Control>
							</Form.Field>

							<Form.Field {form} name="tags">
								<Form.Control let:attrs>
									<Form.Label>Tags</Form.Label>
									<div class="flex flex-row items-start space-x-3 space-y-0 p-1">
										{#if loadingTags}
											<span class="spinner" />
										{:else if tagList.length > 0}
											{#each tagList as tag, index}
												<div class="flex flex-row">
													<Checkbox {...attrs} bind:checked={selectedTagBools[index]} />
													<label class="ml-1">{tag.display_name}</label>
													<div class="group relative ml-1">
														<Info class="h-4 w-4 cursor-pointer text-gray-500" />
														<div
															class="absolute left-0 mt-1 hidden w-64 rounded bg-gray-800 p-2 text-sm text-white group-hover:block"
														>
															{tag.description}
														</div>
													</div>
												</div>
											{/each}
										{:else}
											<p>No Tags available</p>
										{/if}
									</div>
								</Form.Control>
								<Form.FieldErrors />
							</Form.Field>
							<Form.Field {form} name="env" class="col-span-3">
								<Form.FieldErrors />
							</Form.Field>
						</div>
					</div>
					<div class="flex w-1/2 flex-col p-5">
						<Form.Field {form} name="sim_world">
							<Form.Control let:attrs>
								<Form.Label>Simulation World</Form.Label>
								<Select.Root
									selected={{ value: $formData.sim_world, label: $formData.sim_world }}
									onSelectedChange={(v) => {
										v && ($formData.sim_world = v.value);
									}}
								>
									<Select.Trigger {...attrs}>
										<Select.Value placeholder="Select a simulation world" />
									</Select.Trigger>
									<Select.Content>
										{#each $simulationWorlds as option}
											<Select.Item value={option} label={option} />
										{/each}
									</Select.Content>
								</Select.Root>
							</Form.Control>
							<Form.FieldErrors />
						</Form.Field>

						<Form.Field {form} name="autopilot_mode">
							<Form.Control let:attrs>
								<Form.Label>Autopilot Mode</Form.Label>
								<Select.Root
									selected={{ value: $formData.autopilot_mode, label: $formData.autopilot_mode }}
									onSelectedChange={(v) => {
										v && ($formData.autopilot_mode = v.value);
									}}
								>
									<Select.Trigger {...attrs}>
										<Select.Value placeholder="Select an autopilot mode" />
									</Select.Trigger>
									<Select.Content>
										{#each $autopilotModes as option}
											<Select.Item value={option} label={option} />
										{/each}
									</Select.Content>
								</Select.Root>
							</Form.Control>
							<Form.FieldErrors />
						</Form.Field>

						<Form.Field {form} name="flight_mode">
							<Form.Control let:attrs>
								<Form.Label>Flight Mode</Form.Label>
								<Select.Root
									selected={{ value: $formData.flight_mode, label: $formData.flight_mode }}
									onSelectedChange={(v) => {
										v && ($formData.flight_mode = v.value);
									}}
								>
									<Select.Trigger {...attrs}>
										<Select.Value placeholder="Select a flight mode" />
									</Select.Trigger>
									<Select.Content>
										{#each $VTOLModes as option}
											<Select.Item value={option} label={option} />
										{/each}
									</Select.Content>
								</Select.Root>
							</Form.Control>
							<Form.FieldErrors />
						</Form.Field>

						<Form.Field {form} name="mission_plan">
							<Form.Control let:attrs>
								<Form.Label>Mission Plan</Form.Label>
								<Input type="text" {...attrs} bind:value={$formData.mission_plan} />
							</Form.Control>
							<Form.FieldErrors />
						</Form.Field>

						<Form.Field {form} name="wind_mode">
							<Form.Control let:attrs>
								<Form.Label>Wind Mode</Form.Label>
								<Select.Root
									selected={{ value: $formData.wind_mode, label: $formData.wind_mode }}
									onSelectedChange={(v) => {
										v && ($formData.wind_mode = v.value);
									}}
								>
									<Select.Trigger {...attrs}>
										<Select.Value placeholder="Select a wind mode" />
									</Select.Trigger>
									<Select.Content>
										{#each $windModes as option}
											<Select.Item value={option} label={option} />
										{/each}
									</Select.Content>
								</Select.Root>
							</Form.Control>

							<Form.FieldErrors />
						</Form.Field>

						<Form.Field {form} name="wind_speed">
							<Form.Control let:attrs>
								<Form.Label>Wind Speed</Form.Label>
								<Input type="number" min="0" {...attrs} bind:value={$formData.wind_speed} />
							</Form.Control>
							<Form.FieldErrors />
						</Form.Field>
					</div>
				</div>
			</Tabs.Content>
			<Tabs.Content value="second">
				<div class="flex">
					{#if $formData.simulation}
						<div class="flex w-1/3 flex-col p-5">
							<Form.Field {form} name="procedures_simulation">
								<Form.Control let:attrs>
									<Form.Label>Procedures Simulation</Form.Label>
									<Form.Description
										>Enter the list of procedures, each on a new line. (The resulting list will
										appear under this field)
									</Form.Description>
									<Textarea {...attrs} bind:value={$formData.procedures_simulation} />
								</Form.Control>
								<Form.FieldErrors />
								<ol class="mb-5 ml-4 list-inside list-decimal text-xs">
									{#each parse_array($formData.procedures_simulation) as item}
										<li>{item}</li>
									{/each}
								</ol>
							</Form.Field>

							<Form.Field {form} name="expectations_simulation">
								<Form.Control let:attrs>
									<Form.Label>Expectations Simulation</Form.Label>
									<Form.Description
										>Enter the list of expectations, each on a new line. (The resulting list will
										appear under this field)
									</Form.Description>
									<Textarea {...attrs} bind:value={$formData.expectations_simulation} />
								</Form.Control>
								<Form.FieldErrors />
								<ol class="mb-5 ml-4 list-inside list-decimal text-xs">
									{#each parse_array($formData.expectations_simulation) as item}
										<li>{item}</li>
									{/each}
								</ol>
							</Form.Field>
						</div>
					{/if}
					{#if $formData.field_test}
						<div class="flex w-1/3 flex-col p-5">
							<Form.Field {form} name="procedures_field_test">
								<Form.Control let:attrs>
									<Form.Label>Procedures Field Test</Form.Label>
									<Form.Description
										>Enter the list of procedures, each on a new line. (The resulting list will
										appear under this field)
									</Form.Description>
									<Textarea {...attrs} bind:value={$formData.procedures_field_test} />
								</Form.Control>
								<Form.FieldErrors />
								<ol class="mb-5 ml-4 list-inside list-decimal text-xs">
									{#each parse_array($formData.procedures_field_test) as item}
										<li>{item}</li>
									{/each}
								</ol>
							</Form.Field>

							<Form.Field {form} name="expectations_field_test">
								<Form.Control let:attrs>
									<Form.Label>Expectations Field Test</Form.Label>
									<Form.Description
										>Enter the list of expectations, each on a new line. (The resulting list will
										appear under this field)
									</Form.Description>
									<Textarea {...attrs} bind:value={$formData.expectations_field_test} />
								</Form.Control>
								<Form.FieldErrors />
								<ol class="mb-5 ml-4 list-inside list-decimal text-xs">
									{#each parse_array($formData.expectations_field_test) as item}
										<li>{item}</li>
									{/each}
								</ol>
							</Form.Field>
						</div>
					{/if}
					{#if $formData.hil_test}
						<div class="flex w-1/3 flex-col p-5">
							<Form.Field {form} name="procedures_hil_test">
								<Form.Control let:attrs>
									<Form.Label>Procedures HIL Test</Form.Label>
									<Form.Description
										>Enter the list of procedures, each on a new line. (The resulting list will
										appear under this field)
									</Form.Description>
									<Textarea {...attrs} bind:value={$formData.procedures_hil_test} />
								</Form.Control>
								<Form.FieldErrors />
								<ol class="mb-5 ml-4 list-inside list-decimal text-xs">
									{#each parse_array($formData.procedures_hil_test) as item}
										<li>{item}</li>
									{/each}
								</ol>
							</Form.Field>
							<Form.Field {form} name="expectations_hil_test">
								<Form.Control let:attrs>
									<Form.Label>Expectations HIL Test</Form.Label>
									<Form.Description
										>Enter the list of expectations, each on a new line. (The resulting list will
										appear under this field)
									</Form.Description>
									<Textarea {...attrs} bind:value={$formData.expectations_hil_test} />
								</Form.Control>
								<Form.FieldErrors />
								<ol class="mb-5 ml-4 list-inside list-decimal text-xs">
									{#each parse_array($formData.expectations_hil_test) as item}
										<li>{item}</li>
									{/each}
								</ol>
							</Form.Field>
						</div>
					{/if}
				</div>
			</Tabs.Content>
		</Tabs.Root>
	</Card>
	<div class="mt-3 flex justify-end">
		<div use:animationAction>
			{#if form_page === 'first'}
				<Button
					on:click={async () => {
						const result = await form.validateForm({ update: true });
						if (
							!result.valid &&
							Object.keys(result.errors).filter(
								(e) => !e.includes('expectation') && !e.includes('procedure')
							).length > 0
						) {
							triggerAnimation('shake');
							console.log(result.errors);
							return;
						}
						form_page = 'second';
					}}
					variant="secondary"
					>Next
				</Button>
			{:else if !edit}
				<Button
					on:click={() => {
						form_page = 'first';
					}}
					variant="secondary"
					disabled={submitting_form}>Previous</Button
				>
				<Form.Button disabled={submitting_form}>
					{#if submitting_form}
						<span
							class="mr-2 h-2 w-2 animate-spin rounded-full border-b-2 border-t-2 border-secondary"
						></span>
					{/if}
					Register Procedure
				</Form.Button>
			{:else}
				<Button
					on:click={() => {
						form_page = 'first';
					}}
					variant="secondary"
					disabled={submitting_form}>Previous</Button
				>
				<Dialog.Root bind:open={new_version_popup_open}>
					<Dialog.Trigger class={buttonVariants({ variant: 'default' })} disabled={submitting_form}>
						{#if submitting_form}
							<span
								class="mr-2 h-2 w-2 animate-spin rounded-full border-b-2 border-t-2 border-secondary"
							></span>
						{/if}
						Update Procedure
					</Dialog.Trigger>
					<Dialog.Content>
						<Dialog.Header>
							<Dialog.Title>Create a new procedure version?</Dialog.Title>
						</Dialog.Header>
						<Dialog.Description>
							The edits you made can be either applied to this version of the test card, or saved as
							a new version. Saving a new version means that old test reports are not relevant
							anymore.
						</Dialog.Description>
						<Dialog.Footer>
							<Form.Button
								variant="secondary"
								on:click={() => {
									new_version_popup_open = false;
								}}
								>Apply edit to this version
							</Form.Button>
							<Form.Button
								on:click={() => {
									update_procedure_version = true;
									new_version_popup_open = false;
								}}
								>Create a new version
							</Form.Button>
						</Dialog.Footer>
					</Dialog.Content>
				</Dialog.Root>
			{/if}
		</div>
	</div>
</form>

<style>
	.tags-item {
		display: flex;
		align-items: center;
		margin-right: 15px;
	}

	.tags-item > label {
		margin-left: 4px;
	}

	:global(.shake) {
		animation: shake 0.82s cubic-bezier(0.36, 0.07, 0.19, 0.97) both;
		transform: translate3d(0, 0, 0);
		backface-visibility: hidden;
		perspective: 1000px;
	}

	@keyframes shake {
		10%,
		90% {
			transform: translate3d(-1px, 0, 0);
		}

		20%,
		80% {
			transform: translate3d(2px, 0, 0);
		}

		30%,
		50%,
		70% {
			transform: translate3d(-4px, 0, 0);
		}

		40%,
		60% {
			transform: translate3d(4px, 0, 0);
		}
	}

	.spinner {
		border: 4px solid rgba(0, 0, 0, 0.1);
		width: 20px;
		height: 20px;
		border-radius: 50%;
		border-left-color: #09f;
		animation: spin 1s ease infinite;
	}

	@keyframes spin {
		0% {
			transform: rotate(0deg);
		}
		100% {
			transform: rotate(360deg);
		}
	}
</style>
