<script lang="ts">
	import type { TestProcedure } from '$lib/types';

	export let procedures: Array<{
		procedure: TestProcedure;
		results: {
			total: number;
			passed: number;
			failed: number;
			pending: number;
		};
		isImportant: boolean;
	}> = [];
	$: totalProcedures = procedures.length;
	$: totalTests = procedures.reduce((sum, proc) => sum + proc.results.total, 0);

	$: failedProcedures = procedures.filter((proc) => proc.results.failed > 0).length;
	$: passedProcedures = procedures.filter(
		(proc) => proc.results.total > 0 && proc.results.failed === 0 && proc.results.passed > 0
	).length;
	$: pendingProcedures = procedures.filter(
		(proc) =>
			proc.results.total === 0 ||
			(proc.results.failed === 0 && proc.results.passed === 0 && proc.results.pending > 0)
	).length;

	$: passRate = totalProcedures > 0 ? Math.round((passedProcedures / totalProcedures) * 100) : 0;
	$: failRate = totalProcedures > 0 ? Math.round((failedProcedures / totalProcedures) * 100) : 0;
	$: pendingRate =
		totalProcedures > 0 ? Math.round((pendingProcedures / totalProcedures) * 100) : 0;

	$: totalPassedTests = procedures.reduce((sum, proc) => sum + proc.results.passed, 0);
	$: totalFailedTests = procedures.reduce((sum, proc) => sum + proc.results.failed, 0);
	$: totalPendingTests = procedures.reduce((sum, proc) => sum + proc.results.pending, 0);
</script>

<div class="mb-6 grid grid-cols-1 gap-4 md:grid-cols-5">
	<div class="rounded-lg border bg-card p-4 shadow-sm">
		<h3 class="mb-2 text-lg font-medium">Total Procedures</h3>
		<p class="text-3xl font-bold">{totalProcedures}</p>
		<p class="text-sm text-muted-foreground">{totalTests} total tests</p>
	</div>

	<div class="rounded-lg border bg-card p-4 shadow-sm">
		<h3 class="mb-2 text-lg font-medium text-green-600">Passed Procedures</h3>
		<p class="text-3xl font-bold text-green-600">{passedProcedures}</p>
		<p class="text-sm text-muted-foreground">{passRate}% of procedures</p>
		<p class="text-xs text-green-600">{totalPassedTests} passed tests</p>
	</div>

	<div class="rounded-lg border bg-card p-4 shadow-sm">
		<h3 class="mb-2 text-lg font-medium text-red-600">Failed Procedures</h3>
		<p class="text-3xl font-bold text-red-600">{failedProcedures}</p>
		<p class="text-sm text-muted-foreground">{failRate}% of procedures</p>
		<p class="text-xs text-red-600">{totalFailedTests} failed tests</p>
	</div>

	<div class="rounded-lg border bg-card p-4 shadow-sm">
		<h3 class="mb-2 text-lg font-medium text-yellow-600">Pending Procedures</h3>
		<p class="text-3xl font-bold text-yellow-600">{pendingProcedures}</p>
		<p class="text-sm text-muted-foreground">{pendingRate}% of procedures</p>
		<p class="text-xs text-yellow-600">{totalPendingTests} pending tests</p>
	</div>

	<div class="rounded-lg border bg-card p-4 shadow-sm">
		<h3 class="mb-2 text-lg font-medium text-blue-600">Success Rate</h3>
		<p class="text-3xl font-bold text-blue-600">{passRate}%</p>
		<p class="text-sm text-muted-foreground">procedures passed</p>
		<div class="mt-2 text-xs text-muted-foreground">
			<div>✓ {passedProcedures} passed</div>
			<div>✗ {failedProcedures} failed</div>
		</div>
	</div>
</div>
<div class="mb-6">
	<div class="mb-2 flex justify-between text-sm">
		<span class="font-medium">Procedure Status Distribution</span>
		<span class="text-muted-foreground">{totalProcedures} total procedures</span>
	</div>
	<div class="h-4 w-full overflow-hidden rounded-full bg-secondary">
		{#if totalProcedures > 0}
			<div class="flex h-full">
				<div
					class="h-full bg-green-500"
					style="width: {passRate}%"
					title="Passed Procedures: {passedProcedures}"
				></div>
				<div
					class="h-full bg-red-500"
					style="width: {failRate}%"
					title="Failed Procedures: {failedProcedures}"
				></div>
				<div
					class="h-full bg-yellow-500"
					style="width: {pendingRate}%"
					title="Pending Procedures: {pendingProcedures}"
				></div>
			</div>
		{/if}
	</div>
	<div class="mt-1 flex justify-between text-xs text-muted-foreground">
		<span class="text-green-600">{passedProcedures} passed ({passRate}%)</span>
		<span class="text-red-600">{failedProcedures} failed ({failRate}%)</span>
		<span class="text-yellow-600">{pendingProcedures} pending ({pendingRate}%)</span>
	</div>
</div>
