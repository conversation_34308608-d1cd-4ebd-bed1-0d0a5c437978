<script lang="ts">
	import type { TestProcedure } from '$lib/types';
	import * as Table from '$lib/components/ui/table/index.js';

	export let procedures: Array<{
		procedure: TestProcedure;
		results: {
			total: number;
			passed: number;
			failed: number;
			pending: number;
		};
		isImportant: boolean;
	}> = [];
</script>

<Table.Root>
	<Table.Header>
		<Table.Row>
			<Table.Head class="w-[50px]">ID</Table.Head>
			<Table.Head>Procedure</Table.Head>
			<Table.Head class="text-right">Pass/Fail</Table.Head>
			<Table.Head class="text-right">Total</Table.Head>
		</Table.Row>
	</Table.Header>
	<Table.Body>
		{#each procedures as { procedure, results, isImportant }}
			<Table.Row class={isImportant ? 'bg-amber-50' : ''}>
				<Table.Cell class="font-medium">{procedure.external_id}</Table.Cell>
				<Table.Cell>
					<div>
						<span class={isImportant ? 'font-bold text-amber-800' : ''}>
							{procedure.name}
							{#if isImportant}<span class="ml-1 text-xs text-amber-600">(Important)</span>{/if}
						</span>
						<div class="text-xs text-muted-foreground">
							{procedure.group.name}
						</div>
					</div>
				</Table.Cell>
				<Table.Cell>
					<div class="flex flex-col items-end">
						{#if results.total > 0}
							<div class="w-full max-w-[200px]">
								<div class="mb-1 flex justify-between text-xs">
									<span class="text-green-600">{results.passed}</span>
									<span class="text-red-600">{results.failed}</span>
									{#if results.pending > 0}
										<span class="text-yellow-600">{results.pending}</span>
									{/if}
								</div>
								<div class="h-2 w-full overflow-hidden rounded-full bg-secondary">
									{#if results.total > 0}
										<div class="flex h-full">
											<div
												class="h-full bg-green-500"
												style="width: {(results.passed / results.total) * 100}%"
											></div>
											<div
												class="h-full bg-red-500"
												style="width: {(results.failed / results.total) * 100}%"
											></div>
											<div
												class="h-full bg-yellow-500"
												style="width: {(results.pending / results.total) * 100}%"
											></div>
										</div>
									{/if}
								</div>
							</div>
						{:else}
							<span class="text-sm text-muted-foreground">No tests</span>
						{/if}
					</div>
				</Table.Cell>
				<Table.Cell class="text-right">{results.total}</Table.Cell>
			</Table.Row>
		{/each}
	</Table.Body>
</Table.Root>
