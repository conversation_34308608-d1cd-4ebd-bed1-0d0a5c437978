<script lang="ts">
	import type { Test, TestProcedure } from '$lib/types';
	import { Badge } from '$lib/components/ui/badge';
	import { ScrollArea } from '$lib/components/ui/scroll-area/index.js';
	import * as Table from '$lib/components/ui/table';
	import { users, gliders } from '$lib/stores';

	export let selectedProcedure: TestProcedure | null = null;
	export let tests: Test[] = [];

	$: procedureTests = selectedProcedure
		? tests.filter(
				(test) => test.test_procedure_id === selectedProcedure.id && !test.manually_cleared
			)
		: [];

	function formatDate(dateString: string): string {
		return new Date(dateString).toLocaleString(undefined, {
			year: 'numeric',
			month: 'short',
			day: 'numeric',
			hour: '2-digit',
			minute: '2-digit',
			hour12: false
		});
	}

	function getStatusBadge(success_status: boolean | null) {
		if (success_status === true) {
			return { variant: 'default', text: 'Passed', class: 'bg-green-100 text-green-800' };
		} else if (success_status === false) {
			return { variant: 'destructive', text: 'Failed', class: 'bg-red-100 text-red-800' };
		} else {
			return { variant: 'secondary', text: 'Pending', class: 'bg-yellow-100 text-yellow-800' };
		}
	}

	function getTesterEmail(testerId: string): string {
		const user = $users.find((u) => u.id === testerId);
		return user?.email || 'Unknown';
	}

	function getGliderName(gliderId: string, simulation: boolean): string {
		if (simulation) return 'Simulation';
		const glider = $gliders.find((g) => g.id === parseInt(gliderId));
		return glider?.name || 'Unknown';
	}
</script>

{#if selectedProcedure}
	<div class="space-y-4">
		<div class="border-b pb-4">
			<h3 class="text-lg font-semibold">
				{selectedProcedure.external_id} - {selectedProcedure.name}
			</h3>
			<p class="text-sm text-muted-foreground">
				{selectedProcedure.group.name} - {selectedProcedure.test_type}
			</p>
			<div class="mt-2 flex gap-2">
				{#if selectedProcedure.simulation}
					<Badge variant="default">SIM</Badge>
				{/if}
				{#if selectedProcedure.field_test}
					<Badge variant="default">FT</Badge>
				{/if}
				{#if selectedProcedure.hil_test}
					<Badge variant="default">HIL</Badge>
				{/if}
			</div>
		</div>

		<div>
			<h4 class="mb-3 font-medium">Tests ({procedureTests.length})</h4>
			{#if procedureTests.length > 0}
				<ScrollArea class="h-[500px]">
					<div class="overflow-x-auto">
						<Table.Root>
							<Table.Header>
								<Table.Row>
									<Table.Head class="min-w-[100px]">Date</Table.Head>
									<Table.Head class="min-w-[80px]">Status</Table.Head>
									<Table.Head class="min-w-[80px]">Glider</Table.Head>
									<Table.Head class="min-w-[120px]">Tester</Table.Head>
									<Table.Head class="min-w-[80px]">PX4</Table.Head>
									<Table.Head class="min-w-[80px]">GC</Table.Head>
									<Table.Head class="min-w-[80px]">QGC</Table.Head>
									<Table.Head class="min-w-[80px]">Ref-Params</Table.Head>
									<Table.Head class="min-w-[80px]">Force-Sensor</Table.Head>
									<Table.Head class="min-w-[80px]">Landing-Mercury</Table.Head>
									<Table.Head class="min-w-[80px]">R-Auto-FTS</Table.Head>
									<Table.Head class="min-w-[80px]">FTS-Comms</Table.Head>
									<Table.Head class="min-w-[80px]">FTS-Android</Table.Head>
								</Table.Row>
							</Table.Header>
							<Table.Body>
								{#each procedureTests.sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime()) as test}
									<Table.Row>
										<Table.Cell class="text-sm">
											{formatDate(test.date)}
										</Table.Cell>
										<Table.Cell>
											{@const status = getStatusBadge(test.success_status)}
											<Badge class={status.class}>{status.text}</Badge>
										</Table.Cell>
										<Table.Cell class="text-sm">
											{getGliderName(test.glider, test.simulation)}
										</Table.Cell>
										<Table.Cell class="text-sm">
											{getTesterEmail(test.tester_id)}
										</Table.Cell>
										<Table.Cell class="text-sm">
											{test.autopilot_software_version || 'N/A'}
										</Table.Cell>
										<Table.Cell class="text-sm">
											{test.jetson_software_version || 'N/A'}
										</Table.Cell>
										<Table.Cell class="text-sm">
											{test.qgroundcontrol_version || 'N/A'}
										</Table.Cell>
										<Table.Cell class="text-sm">
											{test.reference_parameters_version || 'N/A'}
										</Table.Cell>
										<Table.Cell class="text-sm">
											{test.force_sensor_version || 'N/A'}
										</Table.Cell>
										<Table.Cell class="text-sm">
											{test.landing_station_mercury_version || 'N/A'}
										</Table.Cell>
										<Table.Cell class="text-sm">
											{test.r_autopilot_fts_version || 'N/A'}
										</Table.Cell>
										<Table.Cell class="text-sm">
											{test.fts_comms_server_version || 'N/A'}
										</Table.Cell>
										<Table.Cell class="text-sm">
											{test.fts_trigger_android_app_version || 'N/A'}
										</Table.Cell>
									</Table.Row>
								{/each}
							</Table.Body>
						</Table.Root>
					</div>
				</ScrollArea>
			{:else}
				<p class="py-8 text-center text-muted-foreground">No tests found for this procedure</p>
			{/if}
		</div>
	</div>
{:else}
	<div class="flex h-[600px] items-center justify-center">
		<p class="text-muted-foreground">Select a procedure to view its tests</p>
	</div>
{/if}
