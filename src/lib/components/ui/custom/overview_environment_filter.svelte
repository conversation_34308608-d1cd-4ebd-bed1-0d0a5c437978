<script lang="ts">
	import { Tabs, <PERSON>bs<PERSON><PERSON>, TabsTrigger } from '$lib/components/ui/tabs/index.js';
	import { Badge } from '$lib/components/ui/badge';
	import { createEventDispatcher } from 'svelte';

	export let selectedEnvironment: string = 'all';

	const dispatch = createEventDispatcher();

	function handleEnvironmentChange(value: string) {
		selectedEnvironment = value;
		dispatch('change', { value });
	}

	function getEnvironmentLabel(env: string): string {
		switch (env) {
			case 'sim':
				return 'Simulation';
			case 'irl':
				return 'Field Test';
			case 'hil':
				return 'Hardware-in-Loop';
			default:
				return 'All';
		}
	}
</script>

<div class="mb-4">
	<h3 class="text-md mb-1 font-medium">Test Environment</h3>

	<Tabs
		value={selectedEnvironment}
		onValueChange={(value) => handleEnvironmentChange(value || 'all')}
	>
		<TabsList class="mb-2 grid grid-cols-4">
			<TabsTrigger value="all" class="py-1 text-xs">All</TabsTrigger>
			<TabsTrigger value="sim" class="py-1 text-xs">SIM</TabsTrigger>
			<TabsTrigger value="irl" class="py-1 text-xs">FT</TabsTrigger>
			<TabsTrigger value="hil" class="py-1 text-xs">HIL</TabsTrigger>
		</TabsList>
	</Tabs>

	<div class="mt-2">
		<div class="flex flex-wrap gap-1">
			{#if selectedEnvironment !== 'all'}
				<Badge variant="outline" class="flex items-center gap-1 text-xs">
					Environment: {getEnvironmentLabel(selectedEnvironment)}
				</Badge>
			{/if}
		</div>
	</div>
</div>
