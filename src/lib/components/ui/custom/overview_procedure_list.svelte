<script lang="ts">
	import type { TestProcedure } from '$lib/types';
	import { Badge } from '$lib/components/ui/badge';
	import { cn } from '$lib/utils';

	import { createEventDispatcher } from 'svelte';

	export let procedures: Array<{
		procedure: TestProcedure;
		results: {
			total: number;
			passed: number;
			failed: number;
			pending: number;
		};
		isImportant: boolean;
	}> = [];

	const dispatch = createEventDispatcher();

	function handleProcedureClick(procedure: TestProcedure) {
		dispatch('procedureSelected', { procedure });
	}

	function getCardBackgroundClass(
		results: { total: number; passed: number; failed: number; pending: number },
		isImportant: boolean
	) {
		if (results.total === 0) {
			return isImportant ? 'border-amber-200 bg-amber-50' : 'border-border bg-card';
		}

		if (results.failed > 0) {
			return isImportant
				? 'border-red-300 bg-red-50 ring-1 ring-amber-200'
				: 'border-red-300 bg-red-50';
		}
		if (results.passed > 0 && results.failed === 0 && results.pending === 0) {
			return isImportant
				? 'border-green-300 bg-green-50 ring-1 ring-amber-200'
				: 'border-green-300 bg-green-50';
		}
		return isImportant ? 'border-amber-200 bg-amber-50' : 'border-border bg-card';
	}
</script>

<div class="space-y-2">
	{#each procedures as { procedure, results, isImportant }}
		<div
			class={cn(
				'rounded-lg border p-3',
				getCardBackgroundClass(results, isImportant),
				'cursor-pointer transition-colors hover:opacity-80'
			)}
			on:click={() => handleProcedureClick(procedure)}
			role="button"
			tabindex="0"
			on:keydown={(e) => e.key === 'Enter' && handleProcedureClick(procedure)}
		>
			<div class="flex items-start space-x-3">
				<div class="flex-shrink-0 text-sm font-mono text-muted-foreground">
					#{procedure.external_id}
				</div>
				<div class="flex-1">
					<p class={cn('font-medium', isImportant ? 'font-bold text-amber-800' : '')}>
						{procedure.external_id} - {procedure.name}
					</p>
					<p class="text-sm text-muted-foreground">
						{procedure.group.name} - {procedure.test_type}
					</p>
				</div>
				<div class="flex space-x-1">
					{#if procedure.simulation}
						<Badge variant={procedure.obsolete ? 'outline' : 'default'} class="mr-1">SIM</Badge>
					{/if}
					{#if procedure.field_test}
						<Badge variant={procedure.obsolete ? 'outline' : 'default'} class="mr-1">FT</Badge>
					{/if}
					{#if procedure.hil_test}
						<Badge variant={procedure.obsolete ? 'outline' : 'default'} class="mr-1">HIL</Badge>
					{/if}
					{#if isImportant}
						<Badge variant="secondary" class="mr-1">Important</Badge>
					{/if}
				</div>
			</div>

			<div class="mt-2 flex justify-between text-sm text-muted-foreground">
				<span>Total: {results.total}</span>
				<div class="flex space-x-2">
					<span>✓ {results.passed}</span>
					<span>✗ {results.failed}</span>
					{#if results.pending > 0}
						<span>⏳ {results.pending}</span>
					{/if}
				</div>
			</div>
		</div>
	{/each}
</div>
