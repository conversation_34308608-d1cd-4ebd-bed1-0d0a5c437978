<script lang="ts">
	import { createTest } from '$lib/api';
	import { Button } from '$lib/components/ui/button/index.js';
	import { userProfile, selectedTestProcedure } from '$lib/stores';
	import { Separator } from '$lib/components/ui/separator/index.js';
	import { SquareFunction, TriangleAlert } from 'lucide-svelte';
	import { cn } from '$lib/utils';
	import * as Alert from '$lib/components/ui/alert/index.js';
	import { simRunnerOnline } from '$lib/stores';
	import { type SuperValidated, type Infer, superForm } from 'sveltekit-superforms';
	import { zodClient } from 'sveltekit-superforms/adapters';

	import { z } from 'zod';
	import Card from '../card/card.svelte';
	import { onMount } from 'svelte';

	const formSchema = z.object({});

	type FormSchema = typeof formSchema;

	let submitting_form = false;

	let data: SuperValidated<Infer<FormSchema>> = {};

	const form = superForm(data, {
		validators: zodClient(formSchema),
		validationMethod: 'onblur'
	});

	async function submit_form() {
		submitting_form = true;
		await createTest({
			test_procedure_id: $selectedTestProcedure?.id,
			date: new Date(),
			simulation: true,
			glider: null,
			success_status: null,
			notes: '',
			flight_review_log_url: '',
			automated_execution_status: null,
			tester_id: $userProfile?.id,
			automated: true,
			autopilot_software_version: null,
			jetson_software_version: null,
			qgroundcontrol_version: null,
			reference_parameters_version: null,
			force_sensor_version: null,
			landing_station_mercury_version: null,
			r_autopilot_fts_version: null,
			fts_comms_server_version: null,
			fts_trigger_android_app_version: null,
			manually_cleared: false
		});

		submitting_form = false;

		// location.reload();

		dialog_open = false;
	}

	export let dialog_open;
</script>

{#if !$simRunnerOnline}
	<Alert.Root variant="warning" class="my-3">
		<TriangleAlert class="mr-2 h-5 w-5 text-destructive" />
		<Alert.Title>No SIM runners are online!</Alert.Title>
		<Alert.Description
			>It seems that the automated SIM runner is off. Please check the office PC for that. You can
			still trigger an automated test but it will be queued and executed as soon as the runner is
			back online.</Alert.Description
		>
	</Alert.Root>
{/if}
<Card>
	<div class="flex flex-col p-5">
		<div class="flex items-center justify-between">
			<p>Procedure</p>
			<div class="mx-1 flex-grow">
				<Separator />
			</div>
			<p>{$selectedTestProcedure?.name}</p>
		</div>
		<div class="mt-5 flex items-center justify-between">
			<p>Glider</p>
			<div class="mx-1 flex-grow">
				<Separator />
			</div>
			<p>Sim</p>
		</div>
		<div class="mb-5 mt-5 flex items-center justify-between">
			<p>Tester</p>
			<div class="mx-1 flex-grow">
				<Separator />
			</div>
			<p>{$userProfile?.email}</p>
		</div>
	</div>
</Card>
<div class="mt-3 flex justify-end">
	<Button class="flex justify-end" on:click={submit_form} disabled={submitting_form}>
		{#if submitting_form}
			<span class="mr-2 h-2 w-2 animate-spin rounded-full border-b-2 border-t-2 border-secondary"
			></span>
		{:else}
			<SquareFunction class={cn('mr-2 h-5 w-5 text-muted')} />
		{/if}
		Start Test
	</Button>
</div>
