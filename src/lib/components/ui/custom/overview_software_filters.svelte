<script lang="ts">
	import { createEventDispatcher } from 'svelte';
	import * as Select from '$lib/components/ui/select/index.js';

	export let px4Versions: { name: string }[] = [];
	export let gliderCompanionVersions: { name: string }[] = [];
	export let selectedPx4Version: string = 'all';
	export let selectedGliderCompanionVersion: string = 'all';

	export let qgroundcontrolVersions: string[] = [];
	export let referenceParametersVersions: string[] = [];
	export let forceSensorVersions: string[] = [];
	export let landingStationMercuryVersions: string[] = [];
	export let rAutopilotFTSVersions: string[] = [];
	export let ftsCommsServerVersions: string[] = [];
	export let ftsTriggerAndroidAppVersions: string[] = [];

	export let selectedQGroundControlVersion: string = 'all';
	export let selectedReferenceParametersVersion: string = 'all';
	export let selectedForceSensorVersion: string = 'all';
	export let selectedLandingStationMercuryVersion: string = 'all';
	export let selectedRAutopilotFTSVersion: string = 'all';
	export let selectedFTSCommsServerVersion: string = 'all';
	export let selectedFTSTriggerAndroidAppVersion: string = 'all';
	export let testType: 'all' | 'sim' | 'ft' = 'all';

	const dispatch = createEventDispatcher();

	$: px4Options = createVersionOptions(px4Versions);
	$: gcOptions = createVersionOptions(gliderCompanionVersions);

	function setVersion(type: string, version: string) {
		switch (type) {
			case 'px4':
				selectedPx4Version = version;
				break;
			case 'gc':
				selectedGliderCompanionVersion = version;
				break;
			case 'qgc':
				selectedQGroundControlVersion = version;
				break;
			case 'ref_params':
				selectedReferenceParametersVersion = version;
				break;
			case 'force_sensor':
				selectedForceSensorVersion = version;
				break;
			case 'landing_mercury':
				selectedLandingStationMercuryVersion = version;
				break;
			case 'r_auto_fts':
				selectedRAutopilotFTSVersion = version;
				break;
			case 'fts_comms':
				selectedFTSCommsServerVersion = version;
				break;
			case 'fts_android':
				selectedFTSTriggerAndroidAppVersion = version;
				break;
		}
		dispatch('change', { type, value: version });
	}

	function createVersionOptions(versions: string[]) {
		return [
			{ value: 'all', label: 'All' },
			{ value: 'no_version', label: 'Not Tracked' },
			...versions.map((v) => ({ value: v, label: v }))
		];
	}

	$: qgcOptions = createVersionOptions(qgroundcontrolVersions);
	$: refParamsOptions = createVersionOptions(referenceParametersVersions);
	$: forceSensorOptions = createVersionOptions(forceSensorVersions);
	$: landingMercuryOptions = createVersionOptions(landingStationMercuryVersions);
	$: rAutoFTSOptions = createVersionOptions(rAutopilotFTSVersions);
	$: ftsCommsOptions = createVersionOptions(ftsCommsServerVersions);
	$: ftsAndroidOptions = createVersionOptions(ftsTriggerAndroidAppVersions);
	$: showFtOnlyVersions = testType === 'all' || testType === 'ft';
</script>

<div class="space-y-4">
	<div class="grid grid-cols-1 gap-4 md:grid-cols-2">
		<div>
			<h3 class="mb-1 text-sm font-medium">PX4</h3>
			<Select.Root
				selected={{
					value: selectedPx4Version,
					label: px4Options.find((o) => o.value === selectedPx4Version)?.label || 'All'
				}}
				onSelectedChange={(selected) => selected && setVersion('px4', selected.value)}
			>
				<Select.Trigger class="h-8 text-xs">
					<Select.Value />
				</Select.Trigger>
				<Select.Content>
					{#each px4Options as option}
						<Select.Item value={option.value}>{option.label}</Select.Item>
					{/each}
				</Select.Content>
			</Select.Root>
		</div>

		<div>
			<h3 class="mb-1 text-sm font-medium">Glider-Companion</h3>
			<Select.Root
				selected={{
					value: selectedGliderCompanionVersion,
					label: gcOptions.find((o) => o.value === selectedGliderCompanionVersion)?.label || 'All'
				}}
				onSelectedChange={(selected) => selected && setVersion('gc', selected.value)}
			>
				<Select.Trigger class="h-8 text-xs">
					<Select.Value />
				</Select.Trigger>
				<Select.Content>
					{#each gcOptions as option}
						<Select.Item value={option.value}>{option.label}</Select.Item>
					{/each}
				</Select.Content>
			</Select.Root>
		</div>
	</div>

	<div class="grid grid-cols-2 gap-4 md:grid-cols-4">
		<div>
			<h3 class="mb-1 text-sm font-medium">QGroundControl</h3>
			<Select.Root
				selected={{
					value: selectedQGroundControlVersion,
					label: qgcOptions.find((o) => o.value === selectedQGroundControlVersion)?.label || 'All'
				}}
				onSelectedChange={(selected) => selected && setVersion('qgc', selected.value)}
			>
				<Select.Trigger class="h-8 text-xs">
					<Select.Value />
				</Select.Trigger>
				<Select.Content>
					{#each qgcOptions as option}
						<Select.Item value={option.value}>{option.label}</Select.Item>
					{/each}
				</Select.Content>
			</Select.Root>
		</div>

		<div>
			<h3 class="mb-1 text-sm font-medium">Ref-Params</h3>
			<Select.Root
				selected={{
					value: selectedReferenceParametersVersion,
					label:
						refParamsOptions.find((o) => o.value === selectedReferenceParametersVersion)?.label ||
						'All'
				}}
				onSelectedChange={(selected) => selected && setVersion('ref_params', selected.value)}
			>
				<Select.Trigger class="h-8 text-xs">
					<Select.Value />
				</Select.Trigger>
				<Select.Content>
					{#each refParamsOptions as option}
						<Select.Item value={option.value}>{option.label}</Select.Item>
					{/each}
				</Select.Content>
			</Select.Root>
		</div>
		{#if showFtOnlyVersions}
			<div>
				<h3 class="mb-1 text-sm font-medium">Force-Sensor</h3>
				<Select.Root
					selected={{
						value: selectedForceSensorVersion,
						label:
							forceSensorOptions.find((o) => o.value === selectedForceSensorVersion)?.label || 'All'
					}}
					onSelectedChange={(selected) => selected && setVersion('force_sensor', selected.value)}
				>
					<Select.Trigger class="h-8 text-xs">
						<Select.Value />
					</Select.Trigger>
					<Select.Content>
						{#each forceSensorOptions as option}
							<Select.Item value={option.value}>{option.label}</Select.Item>
						{/each}
					</Select.Content>
				</Select.Root>
			</div>
		{/if}
		{#if showFtOnlyVersions}
			<div>
				<h3 class="mb-1 text-sm font-medium">Landing-Mercury</h3>
				<Select.Root
					selected={{
						value: selectedLandingStationMercuryVersion,
						label:
							landingMercuryOptions.find((o) => o.value === selectedLandingStationMercuryVersion)
								?.label || 'All'
					}}
					onSelectedChange={(selected) => selected && setVersion('landing_mercury', selected.value)}
				>
					<Select.Trigger class="h-8 text-xs">
						<Select.Value />
					</Select.Trigger>
					<Select.Content>
						{#each landingMercuryOptions as option}
							<Select.Item value={option.value}>{option.label}</Select.Item>
						{/each}
					</Select.Content>
				</Select.Root>
			</div>
		{/if}
	</div>
	{#if showFtOnlyVersions}
		<div class="grid grid-cols-1 gap-4 md:grid-cols-3">
			<div>
				<h3 class="mb-1 text-sm font-medium">R-Autopilot (FTS)</h3>
				<Select.Root
					selected={{
						value: selectedRAutopilotFTSVersion,
						label:
							rAutoFTSOptions.find((o) => o.value === selectedRAutopilotFTSVersion)?.label || 'All'
					}}
					onSelectedChange={(selected) => selected && setVersion('r_auto_fts', selected.value)}
				>
					<Select.Trigger class="h-8 text-xs">
						<Select.Value />
					</Select.Trigger>
					<Select.Content>
						{#each rAutoFTSOptions as option}
							<Select.Item value={option.value}>{option.label}</Select.Item>
						{/each}
					</Select.Content>
				</Select.Root>
			</div>
			<div>
				<h3 class="mb-1 text-sm font-medium">FTS-Comms-Server</h3>
				<Select.Root
					selected={{
						value: selectedFTSCommsServerVersion,
						label:
							ftsCommsOptions.find((o) => o.value === selectedFTSCommsServerVersion)?.label || 'All'
					}}
					onSelectedChange={(selected) => selected && setVersion('fts_comms', selected.value)}
				>
					<Select.Trigger class="h-8 text-xs">
						<Select.Value />
					</Select.Trigger>
					<Select.Content>
						{#each ftsCommsOptions as option}
							<Select.Item value={option.value}>{option.label}</Select.Item>
						{/each}
					</Select.Content>
				</Select.Root>
			</div>
			<div>
				<h3 class="mb-1 text-sm font-medium">FTS-Android-App</h3>
				<Select.Root
					selected={{
						value: selectedFTSTriggerAndroidAppVersion,
						label:
							ftsAndroidOptions.find((o) => o.value === selectedFTSTriggerAndroidAppVersion)
								?.label || 'All'
					}}
					onSelectedChange={(selected) => selected && setVersion('fts_android', selected.value)}
				>
					<Select.Trigger class="h-8 text-xs">
						<Select.Value />
					</Select.Trigger>
					<Select.Content>
						{#each ftsAndroidOptions as option}
							<Select.Item value={option.value}>{option.label}</Select.Item>
						{/each}
					</Select.Content>
				</Select.Root>
			</div>
		</div>
	{/if}
</div>
