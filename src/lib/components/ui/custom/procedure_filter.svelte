<script lang="ts">
	import * as Select from '$lib/components/ui/select';
	import Combobox from './combobox.svelte';
	import {
		testProcedureGroups,
		testProcedureEnvironmentNames,
		testProcedureTypeNames
	} from '$lib/stores';
	import { Checkbox } from '../checkbox';
	import type { Tag } from '$lib/types';

	export let selectedGroup: string = '';
	export let selectedType: string | null = null;
	export let selectedEnvironment: string = 'Any';
	export let show_obsolete = false;
	export let tags: Tag[] = [];
	export let selectedTag: string | null = null;
</script>

<div class="mb-2 flex flex-row items-center text-sm">
	<Checkbox bind:checked={show_obsolete} class="mr-2" />
	<div class="space-y-1 leading-none">Show Obsolete Test Procedures</div>
</div>
<div class="flex justify-between">
	<Combobox values={$testProcedureGroups.map((x) => x.name)} bind:value={selectedGroup} />

	<Select.Root
		onSelectedChange={(v) => {
			v && (selectedType = v.value);
		}}
	>
		<Select.Trigger class="w-[180px]">
			<Select.Value placeholder="Type" />
		</Select.Trigger>
		<Select.Content>
			<Select.Item value={null}>Any</Select.Item>
			{#each $testProcedureTypeNames as type}
				<Select.Item value={type}>{type}</Select.Item>
			{/each}
		</Select.Content>
	</Select.Root>

	<Select.Root
		onSelectedChange={(v) => {
			v && (selectedEnvironment = v.value);
		}}
	>
		<Select.Trigger class="w-[180px]">
			<Select.Value placeholder="Test Environment" />
		</Select.Trigger>
		<Select.Content>
			{#each ['Any', ...$testProcedureEnvironmentNames] as env}
				<Select.Item value={env}>{env}</Select.Item>
			{/each}
		</Select.Content>
	</Select.Root>
	<Select.Root
		onSelectedChange={(v) => {
			selectedTag = v ? v.value : null;
		}}
	>
		<Select.Trigger class="w-[180px]">
			<Select.Value placeholder="Tag" />
		</Select.Trigger>
		<Select.Content>
			<Select.Item value={null}>Any</Select.Item>
			{#each tags as tag}
				<Select.Item value={tag.display_name}>{tag.display_name}</Select.Item>
			{/each}
		</Select.Content>
	</Select.Root>
</div>
