<script lang="ts">
	import Check from 'svelte-radix/Check.svelte';
	import CaretSort from 'svelte-radix/CaretSort.svelte';
	import { tick } from 'svelte';
	import * as Command from '$lib/components/ui/command/index.js';
	import * as Popover from '$lib/components/ui/popover/index.js';
	import { Button } from '$lib/components/ui/button/index.js';
	import { cn } from '$lib/utils.js';

	export let values: string[];

	let open = false;
	export let value = '';

	$: selectedValue = values.find((f) => f === value) ?? 'Group';

	// We want to refocus the trigger button when the user selects
	// an item from the list so users can continue navigating the
	// rest of the form with the keyboard.
	function closeAndFocusTrigger(triggerId: string) {
		open = false;
		tick().then(() => {
			try {
				const element = document.getElementById(triggerId);
				if (element && element.focus) {
					element.focus();
				}
			} catch (error) {
				console.warn('Could not focus element:', triggerId, error);
			}
		});
	}
</script>

<Popover.Root bind:open let:ids>
	<Popover.Trigger asChild let:builder>
		<Button
			builders={[builder]}
			variant="outline"
			role="combobox"
			aria-expanded={open}
			class="w-[200px] justify-between font-normal text-primary"
		>
			{selectedValue.length > 20 ? selectedValue.slice(0, 20) + '...' : selectedValue}
			<CaretSort class="ml-2 h-4 w-4 shrink-0 opacity-50" />
		</Button>
	</Popover.Trigger>
	<Popover.Content class="w-[200px] p-0">
		<Command.Root>
			<Command.Input placeholder="Search group..." class="h-9" />
			<Command.Empty>No group found.</Command.Empty>
			<Command.List>
				<Command.Item
					value=""
					onSelect={(currentValue) => {
						value = '';
						closeAndFocusTrigger(ids.trigger);
					}}
				>
					<Check class={cn('mr-2 h-4 w-4', value !== '' && 'text-transparent')} />
					Any
				</Command.Item>
				{#each values as val}
					<Command.Item
						value={val}
						onSelect={(currentValue) => {
							value = currentValue;
							closeAndFocusTrigger(ids.trigger);
						}}
					>
						<Check class={cn('mr-2 h-4 w-4', value !== val && 'text-transparent')} />
						{val}
					</Command.Item>
				{/each}
			</Command.List>
		</Command.Root>
	</Popover.Content>
</Popover.Root>
