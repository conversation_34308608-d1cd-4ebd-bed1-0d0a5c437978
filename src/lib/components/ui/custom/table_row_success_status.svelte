<script lang="ts">
	import { Check, X, Clock, Cog, ShieldX, ClockAlert } from 'lucide-svelte';
	import * as Tooltip from '$lib/components/ui/tooltip';
	import { Root } from 'postcss';

	export let status: boolean;

	export let automated_execution_status: string;

	export let automated: boolean;
</script>

{#if automated}
	{#if automated_execution_status === 'RUNNING'}
		<Tooltip.Root openDelay={200}>
			<Tooltip.Trigger>
				<div class="flying-plane">
					<Cog class="h-5 w-5" />
				</div>
			</Tooltip.Trigger>
			<Tooltip.Content>
				<p>Running automated test</p>
			</Tooltip.Content>
		</Tooltip.Root>
	{:else if automated_execution_status === 'PENDING'}
		<Tooltip.Root openDelay={200}>
			<Tooltip.Trigger>
				<Clock class="h-5 w-5 opacity-70" />
			</Tooltip.Trigger>
			<Tooltip.Content>
				<p>Pending automatic test execution</p>
			</Tooltip.Content>
		</Tooltip.Root>
	{:else if automated_execution_status === 'TIMEOUT'}
		<Tooltip.Root openDelay={200}>
			<Tooltip.Trigger>
				<ClockAlert class="h-5 w-5 text-destructive" />
			</Tooltip.Trigger>
			<Tooltip.Content>
				<p>Automated Runner Timeout</p>
			</Tooltip.Content>
		</Tooltip.Root>
	{:else if automated_execution_status === 'FAILURE'}
		<Tooltip.Root openDelay={200}>
			<Tooltip.Trigger>
				<ShieldX class="h-5 w-5 text-destructive" />
			</Tooltip.Trigger>
			<Tooltip.Content>
				<p>Automated Runner Error</p>
			</Tooltip.Content>
		</Tooltip.Root>
	{:else}
		<Tooltip.Root openDelay={200}>
			<Tooltip.Trigger>0/0</Tooltip.Trigger>
			<Tooltip.Content>
				<p>0/0 Checks passed</p>
			</Tooltip.Content>
		</Tooltip.Root>
	{/if}
{:else if status === true}
	<Tooltip.Root openDelay={200}>
		<Tooltip.Trigger>
			<Check class="h-5 w-5" />
		</Tooltip.Trigger>
		<Tooltip.Content>
			<p>Reported as passed</p>
		</Tooltip.Content>
	</Tooltip.Root>
{:else}
	<Tooltip.Root openDelay={200}>
		<Tooltip.Trigger>
			<X class="h-5 w-5 text-destructive" />
		</Tooltip.Trigger>
		<Tooltip.Content>
			<p>Reported as failed</p>
		</Tooltip.Content>
	</Tooltip.Root>
{/if}

<style>
	@keyframes fly {
		0% {
			transform: rotate(0deg);
		}
		100% {
			transform: rotate(360deg);
		}
	}

	:global(.flying-plane) {
		animation: fly 5s infinite ease-in-out; /* Sinusoidal motion */
	}
</style>
