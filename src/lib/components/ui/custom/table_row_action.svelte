<script lang="ts">
	import * as DropdownMenu from '$lib/components/ui/dropdown-menu';
	import { Button } from '$lib/components/ui/button';
	import { Ellipsis, ExternalLink } from 'lucide-svelte';
	import * as Dialog from '$lib/components/ui/dialog';
	import ReportManualTestForm from '$lib/components/ui/custom/report_manual_test_form.svelte';

	export let flight_review_log_url: string;
	export let update_test_id: number;
	export let update_data: {
		date: Date;
		simulation: boolean;
		glider: number;
		success_status: boolean;
		notes: string;
		flight_review_log_url: string;
		autopilot_software_version?: string;
		jetson_software_version?: string;
		manually_cleared?: boolean;
	};

	export let edit_form_user_email: string | null = null;

	let dialog_open = false;
</script>

<div class="flex items-center gap-1">
	{#if flight_review_log_url !== ''}
		<Button
			variant="ghost"
			size="icon"
			class="h-8 w-8 p-0"
			on:click={() => window.open(flight_review_log_url, '_blank')}
		>
			<span class="sr-only">Open Flight Review Log</span>
			<ExternalLink class="h-4 w-4" />
		</Button>
	{/if}
	<Dialog.Root bind:open={dialog_open}>
		<DropdownMenu.Root>
			<DropdownMenu.Trigger asChild let:builder>
				<Button variant="ghost" builders={[builder]} size="icon" class="relative h-8 w-8 p-0">
					<span class="sr-only">Open menu</span>
					<Ellipsis class="h-4 w-4" />
				</Button>
			</DropdownMenu.Trigger>
			<DropdownMenu.Content>
				<DropdownMenu.Item>
					<Dialog.Trigger class="w-full text-left">Edit Report</Dialog.Trigger>
				</DropdownMenu.Item>
				{#if flight_review_log_url !== ''}
					<DropdownMenu.Item href={flight_review_log_url} target="_blank"
						>Open Flight Review Log</DropdownMenu.Item
					>
				{/if}
			</DropdownMenu.Content>
		</DropdownMenu.Root>
	</Dialog.Root>
</div>

<Dialog.Root bind:open={dialog_open}>
	<Dialog.Content>
		<Dialog.Header>
			<Dialog.Title>Edit Test Report</Dialog.Title>
		</Dialog.Header>
		<Dialog.Description>
			<ReportManualTestForm
				bind:dialog_open
				{update_test_id}
				data={update_data}
				{edit_form_user_email}
			/>
		</Dialog.Description>
	</Dialog.Content>
</Dialog.Root>
