<script lang="ts">
	import { readable } from 'svelte/store';
	import { createTable, Render, Subscribe, createRender } from 'svelte-headless-table';
	import TableRowAction from './table_row_action.svelte';
	import TableRowNotes from './table_row_notes.svelte';
	import TableRowSuccessStatus from './table_row_success_status.svelte';
	import type { Test } from '$lib/types';
	import { fetchUserProfile, fetchGliderById } from '$lib/api';
	import * as Table from '$lib/components/ui/table/index.js';

	export let procedure_id = 1;

	export let data: Test[];

	$: tableData = readable(
		data
			.filter((test) => test.test_procedure_id === procedure_id && !test.manually_cleared)
			.map((x) => {
				return {
					...x,
					date: new Date(x.date).toLocaleString(undefined, {
						year: 'numeric',
						month: 'short',
						day: 'numeric',
						hour: '2-digit',
						minute: '2-digit',
						hour12: false
					}),
					tester_email: fetchUserProfile(x.tester_id).email,
					glider: x.simulation
						? 'Simulation'
						: (x.glider && fetchGliderById(parseInt(x.glider))?.name) || 'Unknown',
					px4_version:
						x.autopilot_software_version === 'not_tracked' || !x.autopilot_software_version
							? 'N/A'
							: x.autopilot_software_version,
					glider_companion_version:
						x.jetson_software_version === 'not_tracked' || !x.jetson_software_version
							? 'N/A'
							: x.jetson_software_version,
					qgroundcontrol_version:
						x.qgroundcontrol_version === 'not_tracked' || !x.qgroundcontrol_version
							? 'N/A'
							: x.qgroundcontrol_version,
					reference_parameters_version:
						x.reference_parameters_version === 'not_tracked' || !x.reference_parameters_version
							? 'N/A'
							: x.reference_parameters_version,
					force_sensor_version:
						x.force_sensor_version === 'not_tracked' || !x.force_sensor_version
							? 'N/A'
							: x.force_sensor_version,
					landing_station_mercury_version:
						x.landing_station_mercury_version === 'not_tracked' ||
						!x.landing_station_mercury_version
							? 'N/A'
							: x.landing_station_mercury_version,
					r_autopilot_fts_version:
						x.r_autopilot_fts_version === 'not_tracked' || !x.r_autopilot_fts_version
							? 'N/A'
							: x.r_autopilot_fts_version,
					fts_comms_server_version:
						x.fts_comms_server_version === 'not_tracked' || !x.fts_comms_server_version
							? 'N/A'
							: x.fts_comms_server_version,
					fts_trigger_android_app_version:
						x.fts_trigger_android_app_version === 'not_tracked' ||
						!x.fts_trigger_android_app_version
							? 'N/A'
							: x.fts_trigger_android_app_version,
					status_info: {
						success_status: x.success_status,
						automated: x.automated,
						automated_execution_status: x.automated_execution_status
					},
					action_menu_data: {
						flight_review_log_url: x.flight_review_log_url,
						update_test_id: x.id,
						update_data: {
							date: new Date(x.date),
							simulation: x.simulation,
							glider: x.glider ? parseInt(x.glider) : 0,
							success_status: x.success_status,
							notes: x.notes,
							flight_review_log_url: x.flight_review_log_url,
							autopilot_software_version: x.autopilot_software_version,
							jetson_software_version: x.jetson_software_version,
							manually_cleared: x.manually_cleared || false
						},
						edit_form_user_email: fetchUserProfile(x.tester_id).email
					}
				};
			})
			.sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime())
	);

	$: table = createTable(tableData);

	$: columns = table.createColumns([
		table.column({
			accessor: 'status_info',
			header: 'Status',
			cell: ({ value }) => {
				return createRender(TableRowSuccessStatus, {
					status: value.success_status,
					automated: value.automated,
					automated_execution_status: value.automated_execution_status
				});
			}
		}),
		table.column({
			accessor: 'date',
			header: 'Date'
		}),
		table.column({
			accessor: 'tester_email',
			header: 'Tester'
		}),
		table.column({
			accessor: 'glider',
			header: 'Glider'
		}),
		table.column({
			accessor: 'px4_version',
			header: 'PX4'
		}),
		table.column({
			accessor: 'glider_companion_version',
			header: 'GC'
		}),
		table.column({
			accessor: 'qgroundcontrol_version',
			header: 'QGC'
		}),
		table.column({
			accessor: 'reference_parameters_version',
			header: 'Ref-Params'
		}),
		table.column({
			accessor: 'force_sensor_version',
			header: 'Force-Sensor'
		}),
		table.column({
			accessor: 'landing_station_mercury_version',
			header: 'Landing-Mercury'
		}),
		table.column({
			accessor: 'r_autopilot_fts_version',
			header: 'R-Auto-FTS'
		}),
		table.column({
			accessor: 'fts_comms_server_version',
			header: 'FTS-Comms'
		}),
		table.column({
			accessor: 'fts_trigger_android_app_version',
			header: 'FTS-Android'
		}),
		table.column({
			accessor: 'notes',
			header: '',
			cell: ({ value }) => {
				return createRender(TableRowNotes, { notes: value });
			}
		}),
		table.column({
			accessor: 'action_menu_data',
			header: '',
			cell: ({ value }) => {
				return createRender(TableRowAction, {
					flight_review_log_url: value.flight_review_log_url,
					update_test_id: value.update_test_id,
					update_data: value.update_data,
					edit_form_user_email: value.edit_form_user_email
				});
			}
		})
	]);

	$: ({ headerRows, pageRows, tableAttrs, tableBodyAttrs } = table.createViewModel(columns));
</script>

<div class="overflow-x-auto rounded-md border">
	<Table.Root {...$tableAttrs}>
		<Table.Header>
			{#each $headerRows as headerRow}
				<Subscribe rowAttrs={headerRow.attrs()}>
					<Table.Row>
						{#each headerRow.cells as cell (cell.id)}
							<Subscribe attrs={cell.attrs()} let:attrs props={cell.props()}>
								<Table.Head {...attrs}>
									{#if cell.id === 'success_status'}
										<div class="ml-4">
											<Render of={cell.render()} />
										</div>
									{:else}
										<Render of={cell.render()} />
									{/if}
								</Table.Head>
							</Subscribe>
						{/each}
					</Table.Row>
				</Subscribe>
			{/each}
		</Table.Header>
		<Table.Body {...$tableBodyAttrs}>
			{#each $pageRows as row (row.id)}
				<Subscribe rowAttrs={row.attrs()} let:rowAttrs>
					<Table.Row {...rowAttrs}>
						{#each row.cells as cell (cell.id)}
							<Subscribe attrs={cell.attrs()} let:attrs>
								<Table.Cell {...attrs} class={cell.id != 'success_status' ? 'w-1/3' : ''}>
									{#if cell.id === 'success_status'}
										<div class="mx-4">
											<Render of={cell.render()} />
										</div>
									{:else}
										<Render of={cell.render()} />
									{/if}
								</Table.Cell>
							</Subscribe>
						{/each}
					</Table.Row>
				</Subscribe>
			{/each}
		</Table.Body>
	</Table.Root>
</div>
