import { writable } from 'svelte/store';
import type { Writable } from 'svelte/store';
import type { TestProcedure, Group, Glider, TestDate, Tag } from './types';
import type { KeycloakProfile } from 'keycloak-js';
import Keycloak from 'keycloak-js';

export const testProcedures: Writable<TestProcedure[]> = writable([]);
export const testProcedureGroups: Writable<Group[]> = writable([]);
export const testProcedureEnvironmentNames: Writable<string[]> = writable([]);
export const testProcedureTypeNames: Writable<string[]> = writable([]);
export const selectedTestProcedure: Writable<TestProcedure | null> = writable(null);
export const VTOLModes: Writable<string[]> = writable([]);
export const autopilotModes: Writable<string[]> = writable([]);
export const simulationWorlds: Writable<string[]> = writable([]);
export const windModes: Writable<string[]> = writable<string[]>([]);
export const gliders: Writable<Glider[]> = writable([]);
export const users: Writable<KeycloakProfile[]> = writable([]);
export const simRunnerOnline: Writable<boolean> = writable(false);
export const testProceduresMaxVersion: Writable<{ [key: string]: number }> = writable({});
export const testDates: Writable<TestDate[]> = writable([]);
export const tags: Writable<Tag[]> = writable([]);

export const userProfile: Writable<KeycloakProfile | null> = writable(null);
export const keycloakClient: Writable<Keycloak | null> = writable(null);
