import { browser } from '$app/environment';

let authRealm = '';
let authUrl = '';
let authClientId = '';
let backendUrl = '';
let msGliderUrl = '';

if (browser) {
	authClientId = document.getElementById('app')?.dataset?.authClientId ?? '$PUBLIC_AUTH_CLIENT_ID';
	authRealm = document.getElementById('app')?.dataset?.authRealm ?? '$PUBLIC_AUTH_REALM';
	authUrl = document.getElementById('app')?.dataset?.authUrl ?? '$PUBLIC_AUTH_URL';
	backendUrl = document.getElementById('app')?.dataset?.backendUrl ?? '$PUBLIC_BACKEND_URL';
	msGliderUrl = document.getElementById('app')?.dataset?.msGliderUrl ?? '$PUBLIC_MS_GLIDER_URL';
}

export default {
	authClientId,
	authUrl,
	authRealm,
	backendUrl,
	msGliderUrl
};
