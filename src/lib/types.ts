export interface TestProcedureSummary {
	id: number;
	name: string;
	irl: boolean;
	hil: boolean;
	sim: boolean;
}

export interface Tag {
	id: number;
	display_name: string;
	description: string;
	is_default: boolean;
}

export interface TestProcedure {
	id: number;
	name: string;
	version: number;
	obsolete: boolean;
	external_id: number;
	test_type: string;
	group: Group;
	priority_level: string;
	simulation: boolean;
	field_test: boolean;
	hil_test: boolean;
	automated: boolean;
	qgroundcontrol: boolean;
	precland_docker: boolean;
	mdp: boolean;
	sim_world?: string | null;
	autopilot_mode: string;
	vtol_mode: string;
	mission_plan?: string | null;
	wind_mode: string;
	wind_speed: number;
	procedures_simulation: string[];
	expectations_simulation: string[];
	procedures_field: string[];
	expectations_field: string[];
	procedures_hil: string[];
	expectations_hil: string[];
	tags: Tag[];
	tag_ids: number[];
}

export interface KeycloakProfile {
	id?: string;
	username?: string;
	email?: string;
	firstName?: string;
	lastName?: string;
	enabled?: boolean;
	emailVerified?: boolean;
	totp?: boolean;
	createdTimestamp?: number;
	attributes?: {
		[key: string]: string[];
	};
}

export interface Test {
	test_procedure_id: number;
	glider: string;
	success_status: boolean;
	notes: string;
	id: number;
	date: string;
	flight_review_log_url: string;
	automated_execution_status: string;
	tester_id: string;
	simulation: boolean;
	automated: boolean;
	autopilot_software_version?: string;
	jetson_software_version?: string;
	qgroundcontrol_version?: string;
	reference_parameters_version?: string;
	force_sensor_version?: string;
	landing_station_mercury_version?: string;
	r_autopilot_fts_version?: string;
	fts_comms_server_version?: string;
	fts_trigger_android_app_version?: string;
	manually_cleared?: boolean;
}

export interface TestCreationRequest {
	test_procedure_id: number;
	glider: string | null;
	success_status: boolean | null;
	notes: string;
	flight_review_log_url: string;
	automated_execution_status: string | null;
	user_id: string;
	automated: boolean;
	autopilot_software_version?: string;
	jetson_software_version?: string;
	qgroundcontrol_version?: string;
	reference_parameters_version?: string;
	force_sensor_version?: string;
	landing_station_mercury_version?: string;
	r_autopilot_fts_version?: string;
	fts_comms_server_version?: string;
	fts_trigger_android_app_version?: string;
	manually_cleared?: boolean;
}

export interface Group {
	id: number; // The unique identifier of the group
	name: string; // The name of the group
}

export interface Glider {
	id: number;
	name: string;
	inUse: boolean;
}

export type OIDCUserInfo = {
	sub: string; // Subject identifier, unique per user
	name?: string; // Full name of the user
	given_name?: string; // First name of the user
	family_name?: string; // Last name of the user
	preferred_username?: string; // Preferred username
	email?: string; // Email address
	email_verified?: boolean; // Whether the email has been verified
	picture?: string; // URL to the user's profile picture
	birthdate?: string; // The user's birthdate
	gender?: string; // The user's gender
	phone_number?: string; // Phone number
	phone_number_verified?: boolean; // Whether the phone number has been verified
	address?: {
		street_address?: string;
		locality?: string;
		region?: string;
		postal_code?: string;
		country?: string;
	}; // User's address information
	updated_at?: number; // Time when the user's information was last updated
};

export type TestDate = {
	test_procedure_id: number;
	latest_test_date: string | null;
};

export type TestUpdate = {
	success_status: boolean;
	notes: string;
	flight_review_log_url: string;
	simulation: boolean;
	glider: string | null;
	date: Date;
	autopilot_software_version?: string;
	jetson_software_version?: string;
	qgroundcontrol_version?: string;
	reference_parameters_version?: string;
	force_sensor_version?: string;
	landing_station_mercury_version?: string;
	r_autopilot_fts_version?: string;
	fts_comms_server_version?: string;
	fts_trigger_android_app_version?: string;
	manually_cleared?: boolean;
};

export interface SoftwareVersion {
	id: number;
	name: string;
	createdAt: string;
	updatedAt: string;
	softwareVersionType: SoftwareVersionType;
}

export interface SoftwareVersionType {
	id: number;
	name: string;
	createdAt: string;
	updatedAt: string;
}

export interface CreateSoftwareVersionDto {
	name: string;
	createdAt: string;
	updatedAt: string;
	softwareVersionTypeId: number;
}

export interface RCFilterState {
	[baseVersion: string]: string[];
}

export interface ParsedVersion {
	baseVersion: string;
	rcType: string;
	fullVersion: string;
}

export interface VersionGroup {
	baseVersion: string;
	versions: ParsedVersion[];
}
