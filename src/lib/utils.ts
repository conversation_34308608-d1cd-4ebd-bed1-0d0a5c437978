import { type ClassValue, clsx } from 'clsx';
import { twMerge } from 'tailwind-merge';
import { cubicOut } from 'svelte/easing';
import type { TransitionConfig } from 'svelte/transition';
import type { TestProcedure } from './types';
import { testDates } from './stores';
import { get } from 'svelte/store';

export function cn(...inputs: ClassValue[]) {
	return twMerge(clsx(inputs));
}

type FlyAndScaleParams = {
	y?: number;
	x?: number;
	start?: number;
	duration?: number;
};

export const flyAndScale = (
	node: Element,
	params: FlyAndScaleParams = { y: -8, x: 0, start: 0.95, duration: 150 }
): TransitionConfig => {
	const style = getComputedStyle(node);
	const transform = style.transform === 'none' ? '' : style.transform;

	const scaleConversion = (valueA: number, scaleA: [number, number], scaleB: [number, number]) => {
		const [minA, maxA] = scaleA;
		const [minB, maxB] = scaleB;

		const percentage = (valueA - minA) / (maxA - minA);
		const valueB = percentage * (maxB - minB) + minB;

		return valueB;
	};

	const styleToString = (style: Record<string, number | string | undefined>): string => {
		return Object.keys(style).reduce((str, key) => {
			if (style[key] === undefined) return str;
			return str + `${key}:${style[key]};`;
		}, '');
	};

	return {
		duration: params.duration ?? 200,
		delay: 0,
		css: (t) => {
			const y = scaleConversion(t, [0, 1], [params.y ?? 5, 0]);
			const x = scaleConversion(t, [0, 1], [params.x ?? 0, 0]);
			const scale = scaleConversion(t, [0, 1], [params.start ?? 0.95, 1]);

			return styleToString({
				transform: `${transform} translate3d(${x}px, ${y}px, 0) scale(${scale})`,
				opacity: t
			});
		},
		easing: cubicOut
	};
};

export const get_procedure_last_test_date = (procedure: TestProcedure): string => {
	const test_dates = get(testDates).filter((td) => td.test_procedure_id == procedure.id);
	if (test_dates.length > 0) {
		const latestTestDate = test_dates[0].latest_test_date;
		if (latestTestDate) {
			return new Intl.DateTimeFormat('en-GB', {
				day: '2-digit',
				month: '2-digit',
				year: 'numeric'
			}).format(new Date(latestTestDate));
		}
	}
	return 'Never Tested';
};

import type { ParsedVersion, VersionGroup, SoftwareVersion } from './types';

export function parseVersion(versionName: string): ParsedVersion {
	const trimmed = versionName.trim();

	const rcMatch = trimmed.match(/^(.+)-rc(\d+)$/i);

	if (rcMatch) {
		return {
			baseVersion: rcMatch[1],
			rcType: `rc${rcMatch[2]}`,
			fullVersion: trimmed
		};
	}
	return {
		baseVersion: trimmed,
		rcType: 'official',
		fullVersion: trimmed
	};
}

export function groupVersionsByBase(versions: SoftwareVersion[]): VersionGroup[] {
	const groups: { [baseVersion: string]: ParsedVersion[] } = {};

	versions.forEach((version) => {
		const parsed = parseVersion(version.name);

		if (!groups[parsed.baseVersion]) {
			groups[parsed.baseVersion] = [];
		}

		groups[parsed.baseVersion].push(parsed);
	});

	return Object.keys(groups)
		.sort((a, b) => b.localeCompare(a))
		.map((baseVersion) => ({
			baseVersion,
			versions: groups[baseVersion].sort((a, b) => {
				if (a.rcType === 'official' && b.rcType !== 'official') return -1;
				if (a.rcType !== 'official' && b.rcType === 'official') return 1;
				return a.rcType.localeCompare(b.rcType);
			})
		}));
}

export function getAvailableRCTypes(versions: SoftwareVersion[], baseVersion: string): string[] {
	return versions
		.filter((v) => parseVersion(v.name).baseVersion === baseVersion)
		.map((v) => parseVersion(v.name).rcType)
		.sort((a, b) => {
			if (a === 'official' && b !== 'official') return -1;
			if (a !== 'official' && b === 'official') return 1;
			return a.localeCompare(b);
		});
}
