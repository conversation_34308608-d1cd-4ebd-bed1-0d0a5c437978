import { gliders, keycloakClient, testProcedures, users } from '$lib/stores';
import type {
	Glider,
	Group,
	SoftwareVersion,
	Tag,
	Test,
	TestCreationRequest,
	TestDate,
	TestProcedure,
	TestUpdate
} from '$lib/types';
import { get } from 'svelte/store';
import type { KeycloakProfile } from 'keycloak-js';
import environment from '$lib/environment';

const BACKEND_URL = environment.backendUrl;

const MS_GLIDER_URL = environment.msGliderUrl;

const AUTH_URL = environment.authUrl;

async function apiRequest<T, BodyType = Record<string, unknown>>(
	endpoint: string,
	method: string = 'GET',
	body?: BodyType,
	params?: URLSearchParams,
	glider_ms?: boolean,
	auth_ms?: boolean
): Promise<T> {
	const token = get(keycloakClient)?.token;

	const headers: Record<string, string> = {
		Authorization: `Bearer ${token}`
	};

	if (body) {
		headers['Content-Type'] = 'application/json';
	}

	let url = (glider_ms ? MS_GLIDER_URL : auth_ms ? AUTH_URL : BACKEND_URL) + endpoint;
	if (params) {
		url += `?${params.toString()}`;
	}

	const response = await fetch(url, {
		method,
		headers,
		body: body ? JSON.stringify(body) : undefined
	});

	const data = await response.json();

	if (!response.ok) {
		throw new Error(`Failed to fetch ${endpoint}`);
	}

	return data;
}

export async function fetchTags(): Promise<Tag[]> {
	try {
		const result = await apiRequest<Tag[]>('/tags/', 'GET');
		return result as Tag[];
	} catch {
		return [];
	}
}

export async function fetchTestProcedures(): Promise<TestProcedure[]> {
	const result = await apiRequest<TestProcedure[]>(
		'/test-procedures/',
		'GET',
		undefined,
		new URLSearchParams({ limit: '5000' })
	);
	testProcedures.set(result.sort((a, b) => a.external_id - b.external_id));
	return result;
}

export async function createTest(test: TestCreationRequest): Promise<Test> {
	return await apiRequest<Test>('/tests/', 'POST', test);
}

export async function createTestProcedure(procedure: TestProcedure): Promise<TestProcedure> {
	return await apiRequest<TestProcedure>('/test-procedures/', 'POST', procedure);
}

export async function updateTestProcedure(
	procedure_id: number,
	procedure: TestProcedure
): Promise<TestProcedure> {
	return await apiRequest<TestProcedure>(`/test-procedures/${procedure_id}/`, 'PATCH', procedure);
}

export async function getLastTestPerProcedure(): Promise<TestDate[]> {
	return await apiRequest<TestDate[]>('/test-procedures/latest-test-dates/', 'GET');
}

export async function updateTest(test_id: number, test: TestUpdate): Promise<Test> {
	return await apiRequest<Test>(`/tests/${test_id}/`, 'PATCH', test);
}

export async function deleteTestProcedure(proc_id: number): Promise<void> {
	await apiRequest<void>(`/test-procedures/version/${proc_id}/`, 'DELETE');
}

export async function fetchTests(
	skip = 0,
	limit = 5000,
	test_procedure_id?: number
): Promise<Test[]> {
	const params = new URLSearchParams({ skip: skip.toString(), limit: limit.toString() });
	if (test_procedure_id !== undefined) {
		params.append('test_procedure_id', test_procedure_id.toString());
	}
	const result = await apiRequest<Test[]>('/tests/', 'GET', undefined, params);

	result.forEach((test) => {
		if (!test.autopilot_software_version) {
			test.autopilot_software_version = '4.0.0';
		} else {
			test.autopilot_software_version = test.autopilot_software_version.trim();
		}

		if (!test.jetson_software_version) {
			test.jetson_software_version = '4.0.0';
		} else {
			test.jetson_software_version = test.jetson_software_version.trim();
		}
		const softwareFields = [
			'qgroundcontrol_version',
			'reference_parameters_version',
			'force_sensor_version',
			'landing_station_mercury_version',
			'r_autopilot_fts_version',
			'fts_comms_server_version',
			'fts_trigger_android_app_version'
		];

		softwareFields.forEach((field) => {
			const fieldValue = (test as Record<string, unknown>)[field];
			if (!fieldValue || fieldValue === 'not_tracked') {
				(test as Record<string, unknown>)[field] = 'not_tracked';
			} else {
				(test as Record<string, unknown>)[field] = (fieldValue as string).trim();
			}
		});

		const rcFields = [
			'r_autopilot_rc_number',
			'glider_companion_rc_number',
			'qgroundcontrol_rc_number',
			'reference_parameters_rc_number',
			'force_sensor_rc_number',
			'landing_station_mercury_rc_number',
			'r_autopilot_fts_rc_number',
			'fts_comms_server_rc_number',
			'fts_trigger_android_app_rc_number'
		];

		rcFields.forEach((field) => {
			const fieldValue = (test as Record<string, unknown>)[field];
			if (fieldValue === null || fieldValue === undefined) {
				(test as Record<string, unknown>)[field] = 0;
			}
		});
	});

	return result;
}

// Get Task Status (GET /tasks/{task_id})
export async function getTaskStatus(task_id: string): Promise<Record<string, unknown>> {
	return await apiRequest<Record<string, unknown>>(`/tasks/${task_id}`);
}

// Get Groups (GET /groups/)
export async function fetchGroups(): Promise<Group[]> {
	return await apiRequest<Group[]>('/groups/');
}

// Create Group (POST /groups/)
export async function createGroup(group: Group): Promise<Group> {
	return await apiRequest<Group>('/groups/', 'POST', group);
}

// Get Test Types (GET /test-types/)
export async function fetchTestTypes(): Promise<string[]> {
	return await apiRequest<string[]>('/test-types/');
}

// Get Environments (GET /environments/)
export async function fetchEnvironments(): Promise<string[]> {
	return await apiRequest<string[]>('/environments/');
}

// Get Sim Worlds (GET /sim-worlds/)
export async function fetchSimWorlds(): Promise<string[]> {
	return await apiRequest<string[]>('/sim-worlds/');
}

// Get Wind Modes (GET /wind-modes/)
export async function fetchWindModes(): Promise<string[]> {
	return await apiRequest<string[]>('/wind-modes/');
}

// Get Autopilot Modes (GET /autopilot-modes/)
export async function fetchAutopilotModes(): Promise<string[]> {
	return await apiRequest<string[]>('/autopilot-modes/');
}

// Get VTOL Modes (GET /vtol-modes/)
export async function fetchVtolModes(): Promise<string[]> {
	return await apiRequest<string[]>('/vtol-modes/');
}

// Get Priority Levels (GET /enums/priority-levels/)
export async function fetchPriorityLevels(): Promise<string[]> {
	return await apiRequest<string[]>('/enums/priority-levels/');
}

export async function fetchGliders(): Promise<Glider[]> {
	return await apiRequest<Glider[]>('/gliders', 'GET', undefined, undefined, true);
}

export async function fetchUsers(): Promise<KeycloakProfile[]> {
	return await apiRequest<KeycloakProfile[]>(
		'/admin/realms/jedsy/users',
		'GET',
		undefined,
		undefined,
		false,
		true
	);
}

export function fetchUserProfile(user_id: string): KeycloakProfile {
	return get(users).find((user) => user.id === user_id) as KeycloakProfile;
}

export function fetchGliderById(glider_id: number): Glider {
	return get(gliders).find((glider) => glider.id === glider_id) as Glider;
}

export async function exportCSV(): Promise<void> {
	const token = get(keycloakClient)?.token;

	// const url = "http://localhost:8000/export-tests";
	const url = BACKEND_URL + '/export-tests/';

	const headers: Record<string, string> = {
		Authorization: `Bearer ${token}`
	};

	const response = await fetch(url, {
		method: 'GET',
		headers
	});

	if (!response.ok) {
		throw new Error(`Failed to download CSV`);
	}

	const blob = await response.blob();

	// Create a temporary URL for the blob and download it
	const downloadUrl = URL.createObjectURL(blob);

	// Use a more SvelteKit-friendly approach
	const link = document.createElement('a');
	link.href = downloadUrl;
	link.download = 'exported-tests.csv';
	link.style.display = 'none';

	// Append, click, and remove in a more controlled way
	document.body.appendChild(link);
	link.click();
	document.body.removeChild(link);

	// Clean up the temporary URL
	URL.revokeObjectURL(downloadUrl);
}

export async function worker_available(): Promise<boolean> {
	try {
		const response = await apiRequest<{ active_workers: unknown[] }>('/active-workers/');
		return response.active_workers.length > 0;
	} catch {
		return false;
	}
}

export async function fetchPx4Versions(): Promise<SoftwareVersion[]> {
	try {
		const result = await apiRequest<SoftwareVersion[]>(
			'/software-versions/software-version-type/1',
			'GET',
			undefined,
			undefined,
			true
		);

		result.forEach((version) => {
			if (version.name) {
				version.name = version.name.trim();
			}
		});

		const has400Version = result.some((version) => version.name === '4.0.0');

		if (!has400Version) {
			result.unshift({
				id: -1,
				name: '4.0.0',
				createdAt: new Date().toISOString(),
				updatedAt: new Date().toISOString(),
				softwareVersionType: {
					id: 1,
					name: 'PX4',
					createdAt: new Date().toISOString(),
					updatedAt: new Date().toISOString()
				}
			});
		}

		return result;
	} catch {
		return [
			{
				id: -1,
				name: '4.0.0',
				createdAt: new Date().toISOString(),
				updatedAt: new Date().toISOString(),
				softwareVersionType: {
					id: 1,
					name: 'PX4',
					createdAt: new Date().toISOString(),
					updatedAt: new Date().toISOString()
				}
			}
		];
	}
}

export async function fetchGliderCompanionVersions(): Promise<SoftwareVersion[]> {
	try {
		const result = await apiRequest<SoftwareVersion[]>(
			'/software-versions/software-version-type/2',
			'GET',
			undefined,
			undefined,
			true
		);

		result.forEach((version) => {
			if (version.name) {
				version.name = version.name.trim();
			}
		});

		const has400Version = result.some((version) => version.name === '4.0.0');

		if (!has400Version) {
			result.unshift({
				id: -2,
				name: '4.0.0',
				createdAt: new Date().toISOString(),
				updatedAt: new Date().toISOString(),
				softwareVersionType: {
					id: 2,
					name: 'Glider Companion',
					createdAt: new Date().toISOString(),
					updatedAt: new Date().toISOString()
				}
			});
		}

		return result;
	} catch {
		return [
			{
				id: -2,
				name: '4.0.0',
				createdAt: new Date().toISOString(),
				updatedAt: new Date().toISOString(),
				softwareVersionType: {
					id: 2,
					name: 'Glider Companion',
					createdAt: new Date().toISOString(),
					updatedAt: new Date().toISOString()
				}
			}
		];
	}
}

async function fetchSoftwareVersionsByType(typeId: number): Promise<SoftwareVersion[]> {
	try {
		const result = await apiRequest<SoftwareVersion[]>(
			`/software-versions/software-version-type/${typeId}`,
			'GET',
			undefined,
			undefined,
			true
		);

		result.forEach((version) => {
			if (version.name) {
				version.name = version.name.trim();
			}
		});

		return result;
	} catch {
		return [];
	}
}

export async function fetchQGroundControlVersions(): Promise<SoftwareVersion[]> {
	return fetchSoftwareVersionsByType(3); // qgc
}
export async function createSoftwareVersionType(name: string): Promise<any> {
	try {
		return await apiRequest('/software-version-types', 'POST', { name }, undefined, true);
	} catch (error) {
		console.error(`Failed to create software version type: ${name}`, error);
		throw error;
	}
}

export async function fetchReferenceParametersVersions(): Promise<SoftwareVersion[]> {
	try {
		return fetchSoftwareVersionsByType(7);
	} catch {
		console.log('Creating Reference Parameters software version type...');
		await createSoftwareVersionType('reference_parameters');
		return fetchSoftwareVersionsByType(7);
	}
}

export async function fetchForceSensorVersions(): Promise<SoftwareVersion[]> {
	try {
		return fetchSoftwareVersionsByType(8);
	} catch {
		console.log('Creating Force Sensor software version type...');
		await createSoftwareVersionType('force_sensor');
		return fetchSoftwareVersionsByType(8);
	}
}

export async function fetchLandingStationMercuryVersions(): Promise<SoftwareVersion[]> {
	try {
		return fetchSoftwareVersionsByType(9);
	} catch {
		console.log('Creating Landing Station Mercury software version type...');
		await createSoftwareVersionType('landing_station_mercury');
		return fetchSoftwareVersionsByType(9);
	}
}

export async function fetchRAutopilotFTSVersions(): Promise<SoftwareVersion[]> {
	return fetchSoftwareVersionsByType(5); // fts_pixhawk
}

export async function fetchFTSCommsServerVersions(): Promise<SoftwareVersion[]> {
	return fetchSoftwareVersionsByType(4); // fts_raspi
}

export async function fetchFTSTriggerAndroidAppVersions(): Promise<SoftwareVersion[]> {
	return fetchSoftwareVersionsByType(6); // fts_app
}

export async function fetchSoftwareVersions(): Promise<SoftwareVersion[]> {
	try {
		const px4Versions = await fetchPx4Versions();
		const gliderCompanionVersions = await fetchGliderCompanionVersions();
		return [...px4Versions, ...gliderCompanionVersions];
	} catch {
		return [];
	}
}
