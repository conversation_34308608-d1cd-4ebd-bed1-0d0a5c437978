FROM node:19-alpine as builder

WORKDIR /usr/src/app

COPY package.json package-lock.json ./

RUN npm install

COPY . .

RUN PUBLIC_BACKEND_URL="\$PUBLIC_BACKEND_URL" \
    PUBLIC_MS_GLIDER_URL="\$PUBLIC_MS_GLIDER_URL" \
    PUBLIC_AUTH_CLIENT_ID="\$PUBLIC_AUTH_CLIENT_ID" \
    PUBLIC_AUTH_REALM="\$PUBLIC_AUTH_REALM" \
    PUBLIC_AUTH_URL="\$PUBLIC_AUTH_URL" \
    npm run build

# hadolint ignore=DL3007
FROM flashspys/nginx-static:latest
COPY nginx.conf /etc/nginx/conf.d/default.conf
COPY --from=builder /usr/src/app/build /static
COPY entrypoint.sh /entrypoint.sh

ENTRYPOINT ["/entrypoint.sh"]
CMD ["nginx", "-g", "daemon off;"]
