---
name: Package
on:
  push:
    branches:
      - 'main'
    tags:
      - 'v*'
  pull_request:
    branches:
      - 'main'

jobs:
  docker:
    runs-on: ubuntu-24.04
    outputs:
      image_digest: ${{ steps.build_push.outputs.digest }}
    permissions:
      id-token: write
      contents: read
    steps:
      - name: Checkout repository
        uses: actions/checkout@v3
      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v1-node16
        with:
          role-to-assume: ${{ secrets.AWS_ECR_PUSH_ROLE }}
          aws-region: ${{ vars.AWS_REGION }}
      - name: Login to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v1
      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v2
        with:
          driver: docker
      - name: Extract metadata (tags, labels) for Docker
        id: meta
        uses: docker/metadata-action@v4
        with:
          images: ${{ vars.AWS_REGISTRY_URL }}
          tags: |
            # set latest tag for default branch
            type=raw,value=latest,enable={{is_default_branch}}
            type=ref,event=branch
            type=ref,event=pr
            type=semver,pattern={{version}}
            type=semver,pattern={{major}}.{{minor}}
      - id: build_push
        name: Build and push Docker image
        uses: docker/build-push-action@v4
        with:
          context: .
          push: true
          tags: ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}
  deploy:
    needs: docker
    runs-on: ubuntu-24.04
    if: startsWith(github.ref, 'refs/tags/v') || github.ref == 'refs/heads/main'
    steps:
      - if: github.ref == 'refs/heads/main'
        run: echo "MS_ENV=dev" >> "$GITHUB_ENV"
      - if: startsWith(github.ref, 'refs/tags/v')
        run: echo "MS_ENV=prod" >> "$GITHUB_ENV"
      - name: Trigger workflow in IAC repo
        run: |
          curl \
          -sSL \
          --fail \
          -X POST \
          -H "Accept: application/vnd.github+json" \
          -H "Authorization: Bearer $IAC_WORKFLOW_TRIGGER_TOKEN" \
          -H "X-GitHub-Api-Version: 2022-11-28" \
          "https://api.github.com/repos/PackageGlider/iac/actions/workflows/deploy.yaml/dispatches" \
          -d "{\"ref\":\"main\",\"inputs\":{\"name\":\"$MS_NAME\",\"digest\":\"$MS_DIGEST\",\"environment\":\"$MS_ENV\"}}"
        env:
          IAC_WORKFLOW_TRIGGER_TOKEN: ${{ secrets.IAC_WORKFLOW_TRIGGER_TOKEN }}
          MS_NAME: ${{ github.event.repository.name }}
          MS_DIGEST: ${{ needs.docker.outputs.image_digest }}
