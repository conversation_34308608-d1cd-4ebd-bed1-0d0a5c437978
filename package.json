{"name": "flight-tests-ui", "version": "0.0.1", "private": true, "scripts": {"dev": "vite dev", "build": "vite build", "preview": "vite preview", "test": "npm run test:integration && npm run test:unit", "check": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json", "check:watch": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json --watch", "lint": "prettier --check . && eslint .", "format": "prettier --write .", "test:integration": "playwright test", "test:unit": "vitest"}, "devDependencies": {"@playwright/test": "^1.28.1", "@sveltejs/adapter-auto": "^3.0.0", "@sveltejs/kit": "^2.0.0", "@sveltejs/vite-plugin-svelte": "^3.0.0", "@tailwindcss/typography": "^0.5.13", "@types/eslint": "^8.56.7", "autoprefixer": "^10.4.19", "eslint": "^9.0.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-svelte": "^2.36.0", "globals": "^15.0.0", "postcss": "^8.4.38", "prettier": "^3.1.1", "prettier-plugin-svelte": "^3.1.2", "prettier-plugin-tailwindcss": "^0.6.4", "svelte": "^4.2.7", "svelte-check": "^3.6.0", "svelte-headless-table": "^0.18.2", "tailwindcss": "^3.4.4", "tslib": "^2.4.1", "typescript": "^5.0.0", "typescript-eslint": "^8.0.0-alpha.20", "vite": "^5.0.3", "vitest": "^1.2.0"}, "type": "module", "dependencies": {"@sveltejs/adapter-static": "^3.0.2", "bits-ui": "^0.21.13", "clsx": "^2.1.1", "cmdk-sv": "^0.0.18", "formsnap": "^1.0.1", "keycloak-js": "^25.0.2", "lucide-svelte": "^0.441.0", "svelte-radix": "^1.1.0", "svelte-trigger-action": "^1.17.0", "sveltekit-superforms": "^2.16.1", "tailwind-merge": "^2.4.0", "tailwind-variants": "^0.2.1", "zod": "^3.23.8"}}